function SuperAdminPaymentGateways() {
    try {
        const [gateways, setGateways] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [selectedGateway, setSelectedGateway] = React.useState(null);
        const [notification, setNotification] = React.useState(null);

        React.useEffect(() => {
            loadPaymentGateways();
        }, []);

        const loadPaymentGateways = async () => {
            try {
                const response = await fetch(window.getApiUrl('/super-admin/payment-gateways'), {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setGateways(data.data || []);
                } else {
                    throw new Error('Failed to load payment gateways');
                }
            } catch (error) {
                console.error('Error loading payment gateways:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load payment gateways'
                });
            } finally {
                setLoading(false);
            }
        };

        const saveGateway = async (gatewayData) => {
            try {
                const url = selectedGateway?.id 
                    ? window.getApiUrl(`/super-admin/payment-gateways/${selectedGateway.id}`)
                    : window.getApiUrl('/super-admin/payment-gateways');
                
                const method = selectedGateway?.id ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method,
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(gatewayData)
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: `Payment gateway ${selectedGateway?.id ? 'updated' : 'created'} successfully`
                    });
                    loadPaymentGateways();
                    setSelectedGateway(null);
                } else {
                    throw new Error('Failed to save payment gateway');
                }
            } catch (error) {
                console.error('Error saving payment gateway:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to save payment gateway'
                });
            }
        };

        const toggleGatewayStatus = async (gateway) => {
            try {
                const response = await fetch(window.getApiUrl(`/super-admin/payment-gateways/${gateway.id}/toggle`), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    setNotification({
                        type: 'success',
                        message: `Payment gateway ${gateway.is_active ? 'deactivated' : 'activated'} successfully`
                    });
                    loadPaymentGateways();
                } else {
                    throw new Error('Failed to toggle gateway status');
                }
            } catch (error) {
                console.error('Error toggling gateway status:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to toggle gateway status'
                });
            }
        };

        const PaymentGatewayForm = ({ gateway, onSave, onCancel }) => {
            const [formData, setFormData] = React.useState({
                name: gateway?.name || '',
                provider: gateway?.provider || 'razorpay',
                api_key: gateway?.api_key || '',
                api_secret: gateway?.api_secret || '',
                webhook_secret: gateway?.webhook_secret || '',
                is_active: gateway?.is_active !== undefined ? gateway.is_active : false,
                is_test_mode: gateway?.is_test_mode !== undefined ? gateway.is_test_mode : true
            });

            const handleSubmit = (e) => {
                e.preventDefault();
                onSave(formData);
            };

            return (
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Gateway Name
                        </label>
                        <input
                            type="text"
                            value={formData.name}
                            onChange={(e) => setFormData({...formData, name: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Provider
                        </label>
                        <select
                            value={formData.provider}
                            onChange={(e) => setFormData({...formData, provider: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        >
                            <option value="razorpay">Razorpay</option>
                            <option value="stripe">Stripe</option>
                            <option value="paypal">PayPal</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            API Key
                        </label>
                        <input
                            type="text"
                            value={formData.api_key}
                            onChange={(e) => setFormData({...formData, api_key: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            API Secret
                        </label>
                        <input
                            type="password"
                            value={formData.api_secret}
                            onChange={(e) => setFormData({...formData, api_secret: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Webhook Secret
                        </label>
                        <input
                            type="password"
                            value={formData.webhook_secret}
                            onChange={(e) => setFormData({...formData, webhook_secret: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="is_active"
                                checked={formData.is_active}
                                onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                                className="mr-2"
                            />
                            <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
                                Active
                            </label>
                        </div>

                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="is_test_mode"
                                checked={formData.is_test_mode}
                                onChange={(e) => setFormData({...formData, is_test_mode: e.target.checked})}
                                className="mr-2"
                            />
                            <label htmlFor="is_test_mode" className="text-sm font-medium text-gray-700">
                                Test Mode
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                        <Button
                            type="button"
                            variant="secondary"
                            onClick={onCancel}
                        >
                            Cancel
                        </Button>
                        <Button type="submit">
                            {gateway ? 'Update' : 'Create'} Gateway
                        </Button>
                    </div>
                </form>
            );
        };

        if (loading) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            );
        }

        return (
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold text-gray-900">Payment Gateway Management</h1>
                    <Button
                        onClick={() => setSelectedGateway({})}
                        icon="fas fa-plus"
                    >
                        Add Payment Gateway
                    </Button>
                </div>

                {/* Payment Gateways List */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Payment Gateways</h2>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Name
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Provider
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        API Key
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Mode
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {gateways.map((gateway) => (
                                    <tr key={gateway.id}>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {gateway.name}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    gateway.provider === 'razorpay' ? 'bg-blue-100 text-blue-800' :
                                                    gateway.provider === 'stripe' ? 'bg-purple-100 text-purple-800' :
                                                    'bg-yellow-100 text-yellow-800'
                                                }`}>
                                                    {gateway.provider.toUpperCase()}
                                                </span>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {gateway.api_key ? `${gateway.api_key.substring(0, 8)}...` : 'Not set'}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                gateway.is_test_mode 
                                                    ? 'bg-yellow-100 text-yellow-800' 
                                                    : 'bg-green-100 text-green-800'
                                            }`}>
                                                {gateway.is_test_mode ? 'Test' : 'Live'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                gateway.is_active 
                                                    ? 'bg-green-100 text-green-800' 
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {gateway.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={() => setSelectedGateway(gateway)}
                                                    className="text-blue-600 hover:text-blue-900"
                                                >
                                                    Edit
                                                </button>
                                                <button
                                                    onClick={() => toggleGatewayStatus(gateway)}
                                                    className={`${
                                                        gateway.is_active 
                                                            ? 'text-red-600 hover:text-red-900' 
                                                            : 'text-green-600 hover:text-green-900'
                                                    }`}
                                                >
                                                    {gateway.is_active ? 'Deactivate' : 'Activate'}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Payment Gateway Form Modal */}
                {selectedGateway && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
                            <h3 className="text-lg font-semibold mb-4">
                                {selectedGateway.id ? 'Edit' : 'Create'} Payment Gateway
                            </h3>
                            <PaymentGatewayForm
                                gateway={selectedGateway}
                                onSave={saveGateway}
                                onCancel={() => setSelectedGateway(null)}
                            />
                        </div>
                    </div>
                )}

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('SuperAdminPaymentGateways component error:', error);
        reportError(error);
        return (
            <div className="p-6 bg-red-50 border border-red-200 rounded-md">
                <h3 className="text-red-800 font-medium">Error Loading Payment Gateways</h3>
                <p className="text-red-600 text-sm mt-1">
                    There was an error loading the payment gateways management page. Please refresh and try again.
                </p>
            </div>
        );
    }
}
