<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test - Bizma</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Login Test Tool</h1>
        <p>Test login <NAME_EMAIL></p>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password" required>
            </div>
            
            <button type="submit" id="submitBtn">Test Login</button>
        </form>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
            <h3>Common Passwords to Try:</h3>
            <ul>
                <li><code>admin123</code> - Default admin password</li>
                <li><code>password</code> - Simple password</li>
                <li><code>superadmin</code> - Role-based password</li>
                <li><code>bizma123</code> - App-based password</li>
            </ul>
            <p><small>If none work, we'll need to reset the password in the database.</small></p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            if (!email || !password) {
                resultDiv.innerHTML = '<div class="error">Please enter both email and password</div>';
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Testing...';
            resultDiv.innerHTML = '<div class="info">Testing login...</div>';
            
            try {
                const response = await fetch('/biz/test-login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>User:</strong> ${data.user.name} (${data.user.email})</p>
                            <p><strong>Role:</strong> ${data.user.role}</p>
                            <p><strong>Status:</strong> ${data.user.status}</p>
                        </div>
                        <div style="margin-top: 15px;">
                            <h4>Debug Information:</h4>
                            <pre>${JSON.stringify(data.debug, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Message:</strong> ${data.message}</p>
                        </div>
                        <div style="margin-top: 15px;">
                            <h4>Debug Information:</h4>
                            <pre>${JSON.stringify(data.debug || data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Test Login';
            }
        });
        
        // Quick test buttons
        const passwords = ['admin123', 'password', 'superadmin', 'bizma123'];
        passwords.forEach(pwd => {
            const btn = document.createElement('button');
            btn.textContent = `Try "${pwd}"`;
            btn.style.margin = '5px';
            btn.style.padding = '5px 10px';
            btn.style.fontSize = '12px';
            btn.style.width = 'auto';
            btn.onclick = () => {
                document.getElementById('password').value = pwd;
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            };
            document.querySelector('.container').appendChild(btn);
        });
    </script>
</body>
</html>
