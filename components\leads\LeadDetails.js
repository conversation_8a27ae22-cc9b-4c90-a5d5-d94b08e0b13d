function LeadDetails({ lead, onEdit, onDelete, onClose }) {
    try {
        const [activeTab, setActiveTab] = React.useState('details'); // details, activities, tasks, notes
        const [activities, setActivities] = React.useState([]);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            fetchLeadActivities();
        }, [lead.objectId]);

        const fetchLeadActivities = async () => {
            try {
                setLoading(true);
                const response = await trickleListObjects(`activity:${lead.objectId}`, 50, true);
                setActivities(response.items);
            } catch (error) {
                console.error('Error fetching lead activities:', error);
            } finally {
                setLoading(false);
            }
        };

        const renderTabContent = () => {
            switch (activeTab) {
                case 'activities':
                    return <LeadActivity leadId={lead.objectId} />;
                case 'tasks':
                    return <LeadTasks leadId={lead.objectId} />;
                case 'notes':
                    return <LeadNotes leadId={lead.objectId} />;
                default:
                    return (
                        <div className="space-y-6">
                            <div>
                                <h3 className="text-lg font-medium mb-4">Contact Information</h3>
                                <div className="space-y-3">
                                    {lead.objectData.email && (
                                        <div className="flex items-center text-gray-600">
                                            <i className="fas fa-envelope w-6"></i>
                                            <span>{lead.objectData.email}</span>
                                        </div>
                                    )}
                                    {lead.objectData.phone && (
                                        <div className="flex items-center text-gray-600">
                                            <i className="fas fa-phone w-6"></i>
                                            <span>{lead.objectData.phone}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-medium mb-4">Lead Details</h3>
                                <div className="space-y-3">
                                    <div>
                                        <span className="text-gray-500">Status</span>
                                        <p>
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                lead.objectData.status === 'won' ? 'bg-green-100 text-green-800' :
                                                lead.objectData.status === 'lost' ? 'bg-red-100 text-red-800' :
                                                lead.objectData.status === 'qualified' ? 'bg-blue-100 text-blue-800' :
                                                lead.objectData.status === 'proposal' ? 'bg-purple-100 text-purple-800' :
                                                lead.objectData.status === 'negotiation' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {lead.objectData.status ? lead.objectData.status.charAt(0).toUpperCase() + lead.objectData.status.slice(1) : 'Unknown'}
                                            </span>
                                        </p>
                                    </div>
                                    <div>
                                        <span className="text-gray-500">Source</span>
                                        <p className="font-medium flex items-center">
                                            <i className={`mr-2 fas ${
                                                lead.objectData.source === 'website' ? 'fa-globe' :
                                                lead.objectData.source === 'referral' ? 'fa-user-friends' :
                                                lead.objectData.source === 'social' ? 'fa-share-alt' :
                                                lead.objectData.source === 'email' ? 'fa-envelope' :
                                                lead.objectData.source === 'event' ? 'fa-calendar' :
                                                'fa-question-circle'
                                            }`}></i>
                                            {lead.objectData.source ? lead.objectData.source.charAt(0).toUpperCase() + lead.objectData.source.slice(1) : 'Unknown'}
                                        </p>
                                    </div>
                                    <div>
                                        <span className="text-gray-500">Priority</span>
                                        <p>
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                lead.objectData.priority === 'high' ? 'bg-red-100 text-red-800' :
                                                lead.objectData.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-green-100 text-green-800'
                                            }`}>
                                                {lead.objectData.priority ? lead.objectData.priority.charAt(0).toUpperCase() + lead.objectData.priority.slice(1) : 'Unknown'}
                                            </span>
                                        </p>
                                    </div>
                                    <div>
                                        <span className="text-gray-500">Value</span>
                                        <p className="font-medium">{formatCurrency(lead.objectData.value)}</p>
                                    </div>
                                    {lead.objectData.followUpDate && (
                                        <div>
                                            <span className="text-gray-500">Follow-up Date</span>
                                            <p className="font-medium">{formatDate(lead.objectData.followUpDate)}</p>
                                        </div>
                                    )}
                                    {lead.objectData.tags && lead.objectData.tags.length > 0 && (
                                        <div>
                                            <span className="text-gray-500">Tags</span>
                                            <div className="mt-1 flex flex-wrap gap-2">
                                                {lead.objectData.tags.map((tag, index) => (
                                                    <span
                                                        key={index}
                                                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                    >
                                                        {tag}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {lead.objectData.notes && (
                                <div>
                                    <h3 className="text-lg font-medium mb-4">Notes</h3>
                                    <p className="text-gray-600 whitespace-pre-line">
                                        {lead.objectData.notes}
                                    </p>
                                </div>
                            )}
                        </div>
                    );
            }
        };

        return (
            <div data-name="lead-details">
                <div className="flex items-start justify-between mb-6">
                    <div>
                        <h2 className="text-2xl font-bold">{lead.objectData.name}</h2>
                        {lead.objectData.company && (
                            <p className="text-gray-600">{lead.objectData.company}</p>
                        )}
                    </div>
                    <div className="flex space-x-4">
                        <Button
                            variant="secondary"
                            icon="fas fa-edit"
                            onClick={onEdit}
                        >
                            Edit
                        </Button>
                        <Button
                            variant="danger"
                            icon="fas fa-trash"
                            onClick={onDelete}
                        >
                            Delete
                        </Button>
                        <Button
                            variant="secondary"
                            icon="fas fa-times"
                            onClick={onClose}
                        >
                            Close
                        </Button>
                    </div>
                </div>

                <div className="mb-6 border-b border-gray-200">
                    <nav className="flex -mb-px">
                        <button
                            className={`mr-8 py-4 text-sm font-medium border-b-2 ${
                                activeTab === 'details'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                            onClick={() => setActiveTab('details')}
                        >
                            Details
                        </button>
                        <button
                            className={`mr-8 py-4 text-sm font-medium border-b-2 ${
                                activeTab === 'activities'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                            onClick={() => setActiveTab('activities')}
                        >
                            Activities
                        </button>
                        <button
                            className={`mr-8 py-4 text-sm font-medium border-b-2 ${
                                activeTab === 'tasks'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                            onClick={() => setActiveTab('tasks')}
                        >
                            Tasks
                        </button>
                        <button
                            className={`mr-8 py-4 text-sm font-medium border-b-2 ${
                                activeTab === 'notes'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                            onClick={() => setActiveTab('notes')}
                        >
                            Notes
                        </button>
                    </nav>
                </div>

                {renderTabContent()}
            </div>
        );
    } catch (error) {
        console.error('LeadDetails component error:', error);
        reportError(error);
        return null;
    }
}
