<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/utils/EmailUtils.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $testEmail = $input['email'] ?? '';
        
        if (empty($testEmail) || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Valid email address required');
        }
        
        // Test email sending
        $result = EmailUtils::testEmail($testEmail);
        echo json_encode($result);
        
    } else {
        // Return current email configuration status
        $config = [
            'smtp_host' => Config::get('SMTP_HOST', 'not configured'),
            'smtp_port' => Config::get('SMTP_PORT', 'not configured'),
            'smtp_username' => Config::get('SMTP_USERNAME', 'not configured'),
            'smtp_from_email' => Config::get('SMTP_FROM_EMAIL', 'not configured'),
            'smtp_from_name' => Config::get('SMTP_FROM_NAME', 'not configured'),
            'php_mail_available' => function_exists('mail'),
            'current_smtp_ini' => ini_get('SMTP'),
            'current_port_ini' => ini_get('smtp_port')
        ];
        
        echo json_encode([
            'status' => 'ready',
            'message' => 'Email configuration status',
            'config' => $config
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
