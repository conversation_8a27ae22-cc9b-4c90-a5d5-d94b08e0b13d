<?php
// Temporarily enable error display for debugging settings issue
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Custom error handler to return JSON errors (but also log them)
function handleError($errno, $errstr, $errfile, $errline) {
    $error = [
        'error' => 'Internal Server Error',
        'message' => $errstr,
        'file' => basename($errfile),
        'line' => $errline,
        'type' => $errno,
        'debug' => [
            'full_file' => $errfile,
            'error_type' => $errno
        ]
    ];
    
    // Log the error
    error_log("API Error: $errstr in $errfile on line $errline");
    
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode($error, JSON_PRETTY_PRINT);
    exit;
}

// Custom exception handler
function handleException($exception) {
    $error = [
        'error' => 'Internal Server Error',
        'message' => $exception->getMessage(),
        'file' => basename($exception->getFile()),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    // Log the exception
    error_log("API Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
    
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode($error, JSON_PRETTY_PRINT);
    exit;
}

// Set error and exception handlers
set_error_handler('handleError');
set_exception_handler('handleException');

// Include security middleware first
require_once 'middleware/security.php';

// Apply security middleware
SecurityMiddleware::init();

// Include required files
require_once 'utils/ErrorHandler.php';
require_once 'db-config.php';
require_once 'handlers/crud-handler.php';
require_once 'handlers/auth-handler.php';

// Initialize error handler
ErrorHandler::init();
require_once 'handlers/super-admin-handler.php';
require_once 'handlers/settings-handler.php';
require_once 'handlers/email-handler.php';

// Disable error display to prevent HTML in JSON responses
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Set headers for JSON response
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Debug logging
error_log("API Request: " . $method . " " . $_SERVER['REQUEST_URI']);

// Get request path
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = explode('/', $uri);

// Extract API endpoint and parameters
// Expected format: /biz/api/api.php/objectType/objectId or /api/api.php/objectType/objectId
// Find the index where 'api.php' appears and extract from there
$apiIndex = array_search('api.php', $uri);
$endpoint = isset($uri[$apiIndex + 1]) ? $uri[$apiIndex + 1] : null;
$objectId = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : null;

// Handle authentication routes
if ($endpoint === 'auth') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'login';

    // Use the auth handler
    handleAuth($action);
    exit;
}

// Handle subscription management routes
if ($endpoint === 'subscription-management') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'current';
    error_log("Subscription Management - Action: " . $action);

    // Include the subscription management handler
    include_once __DIR__ . '/subscription-management.php';
    exit;
}

// Handle super admin routes
if ($endpoint === 'super-admin') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : 'dashboard';
    $subAction = isset($uri[$apiIndex + 3]) ? $uri[$apiIndex + 3] : null;
    error_log("Super Admin - Action: " . $action . ", SubAction: " . $subAction);
    handleSuperAdmin($action, $subAction);
    exit;
}

// Handle settings routes
if ($endpoint === 'settings') {
    $action = isset($uri[$apiIndex + 2]) ? $uri[$apiIndex + 2] : null;

    // Handle email-specific settings routes
    if ($action === 'test-email') {
        handleEmail('test-email');
        exit;
    }

    handleSettings($action, $objectId);
    exit;
}

// Handle error logging endpoint
if ($endpoint === 'errors' && isset($uri[$apiIndex + 2]) && $uri[$apiIndex + 2] === 'log') {
    if ($method === 'POST') {
        // Handle frontend error logging
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input) {
            $errorMessage = isset($input['message']) ? $input['message'] : 'Unknown error';
            $errorFile = isset($input['filename']) ? $input['filename'] : 'Unknown file';
            $errorLine = isset($input['lineno']) ? $input['lineno'] : 'Unknown line';
            $errorStack = isset($input['stack']) ? $input['stack'] : 'No stack trace';
            
            // Log the frontend error
            error_log("Frontend Error: $errorMessage in $errorFile on line $errorLine");
            error_log("Stack trace: $errorStack");
            
            // Return success response
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Error logged successfully']);
            exit;
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid error data']);
            exit;
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed for error logging']);
        exit;
    }
}

// Handle API request based on method
error_log("Handling " . $method . " request for endpoint: " . $endpoint);

switch ($method) {
    case 'GET':
        if ($objectId) {
            // Get single object
            error_log("Getting single object: " . $endpoint . "/" . $objectId);
            getObject($endpoint, $objectId);
        } else {
            // List objects
            error_log("Listing objects: " . $endpoint);
            listObjects($endpoint);
        }
        break;
    case 'POST':
        // Create object
        error_log("Creating object: " . $endpoint);
        createObject($endpoint);
        break;
    case 'PUT':
        // Update object
        error_log("Updating object: " . $endpoint . "/" . $objectId);
        updateObject($endpoint, $objectId);
        break;
    case 'DELETE':
        // Delete object
        error_log("Deleting object: " . $endpoint . "/" . $objectId);
        deleteObject($endpoint, $objectId);
        break;
    default:
        // Method not allowed
        error_log("Method not allowed: " . $method);
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}
?>
