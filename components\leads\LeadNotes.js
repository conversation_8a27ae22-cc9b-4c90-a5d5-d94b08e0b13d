function LeadNotes({ leadId }) {
    try {
        const [notes, setNotes] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [newNote, setNewNote] = React.useState('');

        React.useEffect(() => {
            fetchNotes();
        }, [leadId]);

        const fetchNotes = async () => {
            try {
                setLoading(true);
                const response = await trickleListObjects(`note:${leadId}`, 50, true);
                setNotes(response.items);
            } catch (error) {
                console.error('Error fetching notes:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleAddNote = async (e) => {
            e.preventDefault();
            if (!newNote.trim()) return;

            try {
                await trickleCreateObject(`note:${leadId}`, {
                    content: newNote,
                    createdAt: new Date().toISOString()
                });

                // Also create an activity for the note
                await trickleCreateObject(`activity:${leadId}`, {
                    type: 'note',
                    description: 'Added a new note',
                    createdAt: new Date().toISOString()
                });

                setNewNote('');
                fetchNotes();
            } catch (error) {
                console.error('Error adding note:', error);
            }
        };

        const handleEditNote = async (noteId, updatedContent) => {
            try {
                await trickleUpdateObject(`note:${leadId}`, noteId, {
                    content: updatedContent,
                    updatedAt: new Date().toISOString()
                });

                // Create activity for note edit
                await trickleCreateObject(`activity:${leadId}`, {
                    type: 'note',
                    description: 'Updated a note',
                    createdAt: new Date().toISOString()
                });

                fetchNotes();
            } catch (error) {
                console.error('Error updating note:', error);
            }
        };

        const handleDeleteNote = async (noteId) => {
            if (window.confirm('Are you sure you want to delete this note?')) {
                try {
                    await trickleDeleteObject(`note:${leadId}`, noteId);
                    
                    // Create activity for note deletion
                    await trickleCreateObject(`activity:${leadId}`, {
                        type: 'note',
                        description: 'Deleted a note',
                        createdAt: new Date().toISOString()
                    });

                    fetchNotes();
                } catch (error) {
                    console.error('Error deleting note:', error);
                }
            }
        };

        return (
            <div data-name="lead-notes" className="space-y-4">
                <form onSubmit={handleAddNote}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Add Note
                        </label>
                        <textarea
                            value={newNote || ''}
                            onChange={(e) => setNewNote(e.target.value)}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Enter your note..."
                        />
                    </div>
                    <div className="mt-2 flex justify-end">
                        <Button
                            type="submit"
                            disabled={!newNote.trim()}
                        >
                            Add Note
                        </Button>
                    </div>
                </form>

                <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Notes History</h3>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <i className="fas fa-spinner fa-spin text-blue-500"></i>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {notes.length === 0 ? (
                                <p className="text-gray-500">No notes yet</p>
                            ) : (
                                notes.map((note, index) => (
                                    <NoteItem 
                                        key={index} 
                                        note={note} 
                                        onEdit={handleEditNote}
                                        onDelete={handleDeleteNote}
                                    />
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('LeadNotes component error:', error);
        reportError(error);
        return null;
    }
}
