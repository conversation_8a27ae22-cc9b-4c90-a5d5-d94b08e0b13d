function TrialBanner({ subscription }) {
    try {
        // Check if subscription is valid and is a trial
        if (!subscription || subscription.planId !== 'trial') return null;
        
        // Calculate days remaining in trial
        const calculateDaysRemaining = () => {
            // Use endDate if available
            if (subscription.endDate) {
                const trialEndDate = new Date(subscription.endDate);
                const currentDate = new Date();
                return Math.ceil((trialEndDate - currentDate) / (1000 * 60 * 60 * 24));
            }
            
            // Otherwise calculate from startDate and trialDuration
            if (subscription.startDate) {
                const startDate = new Date(subscription.startDate);
                const trialDuration = subscription.trialDuration || 14; // Default to 14 days
                const endDate = new Date(startDate);
                endDate.setDate(endDate.getDate() + trialDuration);
                
                const now = new Date();
                return Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
            }
            
            return 0;
        };
        
        const daysRemaining = calculateDaysRemaining();
        
        // Determine urgency level based on days remaining
        const getUrgencyLevel = () => {
            if (daysRemaining <= 0) return 'expired';
            if (daysRemaining <= 3) return 'high';
            if (daysRemaining <= 7) return 'medium';
            return 'low';
        };
        
        const urgency = getUrgencyLevel();
        
        // If trial has expired
        if (urgency === 'expired') {
            return (
                <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded-r-md shadow-sm">
                    <div className="flex flex-col md:flex-row md:items-center">
                        <div className="flex items-center">
                            <div className="py-1"><i className="fas fa-exclamation-circle text-red-500 mr-2 text-lg"></i></div>
                            <div>
                                <p className="font-bold">Trial Expired</p>
                                <p className="text-sm">Your free trial has ended. Please upgrade to continue using all features.</p>
                            </div>
                        </div>
                        <div className="mt-3 md:mt-0 md:ml-auto">
                            <button 
                                onClick={() => {
                                    // Scroll to pricing section if on subscriptions page
                                    const pricingSection = document.querySelector('[data-name="pricing-section"]');
                                    if (pricingSection) {
                                        pricingSection.scrollIntoView({ behavior: 'smooth' });
                                    } else {
                                        // Navigate to subscriptions page
                                        window.dispatchEvent(new CustomEvent('app-navigate', { 
                                            detail: { page: 'subscriptions' } 
                                        }));
                                    }
                                }}
                                className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md text-sm font-medium shadow-sm"
                            >
                                Upgrade Now
                            </button>
                        </div>
                    </div>
                </div>
            );
        }
        
        // For active trials with different urgency levels
        const bannerStyles = {
            high: {
                bg: 'bg-yellow-50',
                border: 'border-yellow-500',
                text: 'text-yellow-800',
                icon: 'text-yellow-500',
                button: 'bg-yellow-600 hover:bg-yellow-700'
            },
            medium: {
                bg: 'bg-blue-50',
                border: 'border-blue-500',
                text: 'text-blue-800',
                icon: 'text-blue-500',
                button: 'bg-blue-600 hover:bg-blue-700'
            },
            low: {
                bg: 'bg-green-50',
                border: 'border-green-500',
                text: 'text-green-800',
                icon: 'text-green-500',
                button: 'bg-green-600 hover:bg-green-700'
            }
        };
        
        const style = bannerStyles[urgency];
        
        return (
            <div className={`${style.bg} border-l-4 ${style.border} ${style.text} p-4 mb-4 rounded-r-md shadow-sm`}>
                <div className="flex flex-col md:flex-row md:items-center">
                    <div className="flex items-center">
                        <div className="py-1"><i className={`fas fa-info-circle ${style.icon} mr-2 text-lg`}></i></div>
                        <div>
                            <p className="font-bold">Trial Active</p>
                            <p className="text-sm">
                                Your free trial expires in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}.
                                {urgency === 'high' && ' Upgrade soon to avoid service interruption!'}
                            </p>
                        </div>
                    </div>
                    <div className="mt-3 md:mt-0 md:ml-auto">
                        <button 
                            onClick={() => {
                                // Scroll to pricing section if on subscriptions page
                                const pricingSection = document.querySelector('[data-name="pricing-section"]');
                                if (pricingSection) {
                                    pricingSection.scrollIntoView({ behavior: 'smooth' });
                                } else {
                                    // Navigate to subscriptions page
                                    window.dispatchEvent(new CustomEvent('app-navigate', { 
                                        detail: { page: 'subscriptions' } 
                                    }));
                                }
                            }}
                            className={`${style.button} text-white py-2 px-4 rounded-md text-sm font-medium shadow-sm`}
                        >
                            Upgrade Now
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('TrialBanner component error:', error);
        return null;
    }
}