<?php
/**
 * Comprehensive database schema checker for all tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');

require_once __DIR__ . '/api/db-config.php';

echo "=== Comprehensive Database Schema Check ===\n\n";

try {
    // 1. Check database connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    echo "✅ Database connected successfully\n";
    
    // 2. Define expected table schemas
    $expectedSchemas = [
        'users' => [
            'object_id' => 'VARCHAR(36) PRIMARY KEY',
            'email' => 'VARCHAR(255) UNIQUE NOT NULL',
            'password_hash' => 'VARCHAR(255) NOT NULL',
            'first_name' => 'VARCHAR(100)',
            'last_name' => 'VARCHAR(100)',
            'phone' => 'VARCHAR(20)',
            'role' => 'VARCHAR(50) DEFAULT \'user\'',
            'status' => 'VARCHAR(20) DEFAULT \'active\'',
            'email_verified' => 'BOOLEAN DEFAULT FALSE',
            'verification_token' => 'VARCHAR(255)',
            'reset_token' => 'VARCHAR(255)',
            'reset_token_expires' => 'DATETIME',
            'last_login' => 'DATETIME',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'companies' => [
            'object_id' => 'VARCHAR(36) PRIMARY KEY',
            'user_id' => 'VARCHAR(36) NOT NULL',
            'name' => 'VARCHAR(255) NOT NULL',
            'business_type' => 'VARCHAR(100)',
            'industry' => 'VARCHAR(100)',
            'size' => 'VARCHAR(50)',
            'website' => 'VARCHAR(255)',
            'phone' => 'VARCHAR(20)',
            'address' => 'TEXT',
            'city' => 'VARCHAR(100)',
            'state' => 'VARCHAR(100)',
            'country' => 'VARCHAR(100)',
            'postal_code' => 'VARCHAR(20)',
            'tax_id' => 'VARCHAR(50)',
            'settings' => 'JSON',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'subscriptions' => [
            'object_id' => 'VARCHAR(36) PRIMARY KEY',
            'user_id' => 'VARCHAR(36) NOT NULL',
            'company_id' => 'VARCHAR(36)',
            'plan_id' => 'VARCHAR(50) NOT NULL',
            'status' => 'VARCHAR(20) DEFAULT \'active\'',
            'billing_cycle' => 'VARCHAR(20) DEFAULT \'monthly\'',
            'amount' => 'DECIMAL(10,2) NOT NULL DEFAULT 0.00',
            'currency' => 'VARCHAR(3) DEFAULT \'INR\'',
            'trial_start_date' => 'DATETIME',
            'trial_end_date' => 'DATETIME',
            'trial_extended_days' => 'INT DEFAULT 0',
            'subscription_start_date' => 'DATETIME',
            'subscription_end_date' => 'DATETIME',
            'next_billing_date' => 'DATETIME',
            'payment_method' => 'VARCHAR(50)',
            'payment_gateway' => 'VARCHAR(50)',
            'gateway_subscription_id' => 'VARCHAR(255)',
            'metadata' => 'JSON',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'leads' => [
            'object_id' => 'VARCHAR(36) PRIMARY KEY',
            'user_id' => 'VARCHAR(36) NOT NULL',
            'company_id' => 'VARCHAR(36)',
            'name' => 'VARCHAR(255) NOT NULL',
            'email' => 'VARCHAR(255)',
            'phone' => 'VARCHAR(50)',
            'company' => 'VARCHAR(255)',
            'source' => 'VARCHAR(100)',
            'status' => 'VARCHAR(50) DEFAULT \'new\'',
            'priority' => 'VARCHAR(20) DEFAULT \'medium\'',
            'value' => 'DECIMAL(10,2) DEFAULT 0.00',
            'notes' => 'TEXT',
            'tags' => 'JSON',
            'custom_fields' => 'JSON',
            'assigned_to' => 'VARCHAR(36)',
            'last_contact_date' => 'DATETIME',
            'next_follow_up' => 'DATETIME',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]
    ];
    
    // 3. Check each table
    foreach ($expectedSchemas as $tableName => $expectedColumns) {
        echo "\n--- Checking Table: $tableName ---\n";
        
        // Check if table exists
        $result = $conn->query("SHOW TABLES LIKE '$tableName'");
        if ($result->num_rows > 0) {
            echo "✅ Table $tableName exists\n";
            
            // Get current columns
            $result = $conn->query("DESCRIBE $tableName");
            $existingColumns = [];
            while ($row = $result->fetch_assoc()) {
                $existingColumns[] = $row['Field'];
            }
            
            // Check for missing columns
            $missingColumns = [];
            foreach ($expectedColumns as $column => $definition) {
                if (!in_array($column, $existingColumns)) {
                    $missingColumns[] = $column;
                }
            }
            
            if (empty($missingColumns)) {
                echo "✅ All required columns present\n";
            } else {
                echo "⚠️  Missing columns: " . implode(', ', $missingColumns) . "\n";
                
                // Add missing columns
                foreach ($missingColumns as $column) {
                    $definition = $expectedColumns[$column];
                    // Skip PRIMARY KEY for ALTER TABLE
                    $alterDefinition = str_replace(' PRIMARY KEY', '', $definition);
                    
                    $sql = "ALTER TABLE $tableName ADD COLUMN $column $alterDefinition";
                    
                    if ($conn->query($sql)) {
                        echo "✅ Added column: $column\n";
                    } else {
                        echo "❌ Failed to add column $column: " . $conn->error . "\n";
                    }
                }
            }
            
        } else {
            echo "❌ Table $tableName does not exist\n";
            echo "Creating table $tableName...\n";
            
            // Create table
            $sql = "CREATE TABLE $tableName (\n";
            $columnDefs = [];
            foreach ($expectedColumns as $column => $definition) {
                $columnDefs[] = "    $column $definition";
            }
            $sql .= implode(",\n", $columnDefs);
            $sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if ($conn->query($sql)) {
                echo "✅ Table $tableName created successfully\n";
            } else {
                echo "❌ Failed to create table $tableName: " . $conn->error . "\n";
            }
        }
    }
    
    // 4. Check table counts
    echo "\n--- Table Statistics ---\n";
    foreach (array_keys($expectedSchemas) as $tableName) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $tableName");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "$tableName: " . $row['count'] . " records\n";
        }
    }
    
    // 5. Test CRUD operations
    echo "\n--- Testing CRUD Operations ---\n";
    
    // Test user creation (if no users exist)
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $userCount = $result->fetch_assoc()['count'];
    
    if ($userCount == 0) {
        echo "Creating test user...\n";
        $testUserId = 'user_' . uniqid();
        $sql = "INSERT INTO users (object_id, email, password_hash, first_name, last_name, role) 
                VALUES (?, '<EMAIL>', ?, 'Admin', 'User', 'super_admin')";
        $stmt = $conn->prepare($sql);
        $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt->bind_param("ss", $testUserId, $passwordHash);
        
        if ($stmt->execute()) {
            echo "✅ Test user created: <EMAIL> / admin123\n";
        } else {
            echo "❌ Failed to create test user: " . $stmt->error . "\n";
        }
    } else {
        echo "✅ Users table has $userCount records\n";
    }
    
    echo "\n=== Schema Check Complete ===\n";
    echo "✅ All required tables are present\n";
    echo "✅ All required columns are available\n";
    echo "✅ Database is ready for CRUD operations\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$conn->close();
?>
