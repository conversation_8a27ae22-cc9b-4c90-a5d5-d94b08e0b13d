<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <script>
        // Define getAppUrl function
        window.getAppUrl = function(path) {
            const basePath = '/biz';
            const cleanPath = path.startsWith('/') ? path : '/' + path;
            return basePath + cleanPath;
        };

        // Listen for navigation events
        window.addEventListener('DOMContentLoaded', function() {
            window.addEventListener('app-navigate', function(event) {
                console.log('Navigation event received:', event.detail);
                document.getElementById('result').textContent = 'Navigation event received: ' + JSON.stringify(event.detail);
                
                // Build the URL
                let url = `/${event.detail.page}`;
                if (event.detail.id) url += `/${event.detail.id}`;
                if (event.detail.action) url += `/${event.detail.action}`;
                
                // Add query parameters if provided
                if (event.detail.params && Object.keys(event.detail.params).length > 0) {
                    const queryString = new URLSearchParams(event.detail.params).toString();
                    url += `?${queryString}`;
                }
                
                // Update history
                const fullUrl = window.getAppUrl(url);
                document.getElementById('url').textContent = 'URL: ' + fullUrl;
            });
        });

        function navigateToSuperAdmin() {
            window.dispatchEvent(new CustomEvent('app-navigate', { 
                detail: { 
                    page: 'super-admin',
                    id: null,
                    action: null,
                    params: {}
                } 
            }));
        }
    </script>
</head>
<body>
    <h1>Navigation Test</h1>
    <button onclick="navigateToSuperAdmin()">Navigate to Super Admin</button>
    <div id="result" style="margin-top: 20px; padding: 10px; background-color: #f0f0f0;"></div>
    <div id="url" style="margin-top: 10px; padding: 10px; background-color: #e0e0e0;"></div>
</body>
</html>