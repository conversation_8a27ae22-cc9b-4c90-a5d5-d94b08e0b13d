function LeadList({ onLeadClick }) {
    try {
        const [leads, setLeads] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [selectedPriority, setSelectedPriority] = React.useState('');
        const [selectedSource, setSelectedSource] = React.useState('');

        React.useEffect(() => {
            fetchLeads();
        }, []);

        const fetchLeads = async () => {
            try {
                setLoading(true);
                const token = localStorage.getItem('authToken');
                const response = await fetch(window.getApiUrl('/lead'), {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    setLeads(data.items || []);
                } else {
                    throw new Error('Failed to fetch leads');
                }
            } catch (error) {
                console.error('Error fetching leads:', error);
                setLeads([]);
            } finally {
                setLoading(false);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters && filters.status ? filters.status : '');
            setSelectedPriority(filters && filters.priority ? filters.priority : '');
            setSelectedSource(filters && filters.source ? filters.source : '');
        };

        const filteredLeads = React.useMemo(() => {
            return leads.filter(lead => {
                const matchesSearch = !searchQuery ||
                    lead.objectData.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (lead.objectData.email && lead.objectData.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (lead.objectData.company && lead.objectData.company.toLowerCase().includes(searchQuery.toLowerCase()));
                
                const matchesStatus = !selectedStatus || lead.objectData.status === selectedStatus;
                const matchesPriority = !selectedPriority || lead.objectData.priority === selectedPriority;
                const matchesSource = !selectedSource || lead.objectData.source === selectedSource;

                return matchesSearch && matchesStatus && matchesPriority && matchesSource;
            });
        }, [leads, searchQuery, selectedStatus, selectedPriority, selectedSource]);

        const leadFilters = [
            {
                id: 'status',
                label: 'Status',
                type: 'select',
                options: [
                    { label: 'New', value: 'new' },
                    { label: 'Contacted', value: 'contacted' },
                    { label: 'Qualified', value: 'qualified' },
                    { label: 'Proposal', value: 'proposal' },
                    { label: 'Negotiation', value: 'negotiation' },
                    { label: 'Won', value: 'won' },
                    { label: 'Lost', value: 'lost' }
                ]
            },
            {
                id: 'priority',
                label: 'Priority',
                type: 'select',
                options: [
                    { label: 'High', value: 'high' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Low', value: 'low' }
                ]
            },
            {
                id: 'source',
                label: 'Source',
                type: 'select',
                options: [
                    { label: 'Website', value: 'website' },
                    { label: 'Referral', value: 'referral' },
                    { label: 'Social Media', value: 'social' },
                    { label: 'Email Campaign', value: 'email' },
                    { label: 'Event', value: 'event' },
                    { label: 'Other', value: 'other' }
                ]
            }
        ];

        const columns = [
            { key: 'name', label: 'Name' },
            { 
                key: 'company', 
                label: 'Company',
                render: (row) => row.objectData.company || '-'
            },
            { 
                key: 'value', 
                label: 'Value',
                render: (row) => formatCurrency(row.objectData.value)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.objectData.status === 'won' ? 'bg-green-100 text-green-800' :
                        row.objectData.status === 'lost' ? 'bg-red-100 text-red-800' :
                        row.objectData.status === 'qualified' ? 'bg-blue-100 text-blue-800' :
                        row.objectData.status === 'proposal' ? 'bg-purple-100 text-purple-800' :
                        row.objectData.status === 'negotiation' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        {row.objectData.status ? row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1) : 'Unknown'}
                    </span>
                )
            },
            {
                key: 'priority',
                label: 'Priority',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.objectData.priority === 'high' ? 'bg-red-100 text-red-800' :
                        row.objectData.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                    }`}>
                        {row.objectData.priority ? row.objectData.priority.charAt(0).toUpperCase() + row.objectData.priority.slice(1) : 'Unknown'}
                    </span>
                )
            },
            {
                key: 'followUpDate',
                label: 'Follow-up Date',
                render: (row) => row.objectData.followUpDate ? formatDate(row.objectData.followUpDate) : '-'
            },
            {
                key: 'source',
                label: 'Source',
                render: (row) => (
                    <span className="inline-flex items-center">
                        <i className={`mr-1 fas ${
                            row.objectData.source === 'website' ? 'fa-globe' :
                            row.objectData.source === 'referral' ? 'fa-user-friends' :
                            row.objectData.source === 'social' ? 'fa-share-alt' :
                            row.objectData.source === 'email' ? 'fa-envelope' :
                            row.objectData.source === 'event' ? 'fa-calendar' :
                            'fa-question-circle'
                        }`}></i>
                        {row.objectData.source ? row.objectData.source.charAt(0).toUpperCase() + row.objectData.source.slice(1) : 'Unknown'}
                    </span>
                )
            }
        ];

        return (
            <div data-name="lead-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search leads..."
                        filters={leadFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredLeads}
                    loading={loading}
                    onRowClick={onLeadClick}
                    emptyMessage="No leads found"
                />
            </div>
        );
    } catch (error) {
        console.error('LeadList component error:', error);
        reportError(error);
        return null;
    }
}
