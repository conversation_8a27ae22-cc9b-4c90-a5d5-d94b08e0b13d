// Inline LoadingSpinner component as a fallback
const InlineLoadingSpinner = ({ size = 'md', text = 'Loading...' }) => {
    const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-12 w-12' : 'h-8 w-8';
    return (
        <div className="flex flex-col items-center justify-center">
            <div className={`animate-spin rounded-full ${sizeClass} border-b-2 border-blue-600`}></div>
            {text && <p className="mt-2 text-gray-600">{text}</p>}
        </div>
    );
};

// Inline ErrorMessage component as a fallback
const InlineErrorMessage = ({ error, onRetry }) => {
    const message = typeof error === 'string' ? error : (error?.message || 'An error occurred');
    return (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 my-4">
            <p className="text-red-700">{message}</p>
            {onRetry && (
                <button
                    onClick={onRetry}
                    className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                >
                    Try Again
                </button>
            )}
        </div>
    );
};

// Inline TrialBanner component as a fallback
const InlineTrialBanner = ({ subscription }) => {
    if (!subscription || subscription.planId !== 'trial') return null;

    return (
        <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-4 rounded-r-md shadow-sm">
            <div className="flex items-center">
                <div className="py-1"><i className="fas fa-info-circle text-blue-500 mr-2 text-lg"></i></div>
                <div>
                    <p className="font-bold">Free Trial Active</p>
                    <p className="text-sm">You're currently on a free trial. Upgrade to unlock all features.</p>
                </div>
            </div>
        </div>
    );
};



function Dashboard() {
    const [stats, setStats] = React.useState({
        totalCustomers: 0,
        activeQuotations: 0,
        pendingInvoices: 0,
        activeContracts: 0,
        totalRevenue: 0,
        monthlyRevenue: 0,
        recentActivities: [],
        upcomingTasks: [],
        chartData: null
    });
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);
    const [refreshing, setRefreshing] = React.useState(false);
    const [lastUpdated, setLastUpdated] = React.useState(null);
    const [subscription, setSubscription] = React.useState(null);
    const [showSubscriptionModal, setShowSubscriptionModal] = React.useState(false);

    // Get authentication context
    const authContext = React.useContext(window.AuthContext);
    const { isAuthenticated, token, user } = authContext || {};

    // Auto-refresh interval
    const [autoRefresh, setAutoRefresh] = React.useState(true);
    const refreshIntervalRef = React.useRef(null);

    const navigate = (page, id = null, action = null, params = {}) => {
        // Use the improved app-navigate event for consistent routing
        window.dispatchEvent(new CustomEvent('app-navigate', { 
            detail: { 
                page,
                id,
                action,
                params
            } 
        }));
    };

    const fetchDashboardData = async (isRefresh = false) => {
        if (isRefresh) {
            setRefreshing(true);
        } else {
            setLoading(true);
        }
        setError(null);
        
        try {
            // Use token from context first, fallback to localStorage
            const authToken = token || localStorage.getItem('authToken');

            if (!authToken) {
                setError('You are not logged in. Please log in to view the dashboard.');
                setLoading(false);
                return;
            }

            // Helper to fetch and handle errors with improved caching
            const fetchWithAuth = async (url, useCache = false) => {
                const cacheKey = `dashboard_${url}`;
                
                // Use the new dataCache utility if available
                if (useCache && !isRefresh && window.dataCache) {
                    const cachedData = window.dataCache.get(cacheKey);
                    if (cachedData) {
                        console.log(`Using cached data for ${url}`);
                        return cachedData;
                    }
                }
                
                // Fallback to localStorage cache if dataCache is not available
                if (useCache && !isRefresh && !window.dataCache) {
                    const cached = localStorage.getItem(cacheKey);
                    if (cached) {
                        const { data, timestamp } = JSON.parse(cached);
                        if (Date.now() - timestamp < 5 * 60 * 1000) { // 5 minutes
                            return data;
                        }
                    }
                }

                const res = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': isRefresh ? 'no-cache' : 'max-age=300'
                    }
                });

                if (!res.ok) {
                    let msg = `API error: ${res.status} ${res.statusText}`;
                    try {
                        const errJson = await res.json();
                        msg = errJson.message || msg;
                    } catch (e) {}
                    throw new Error(msg);
                }
                
                const data = await res.json();
                
                // Cache successful responses
                if (useCache) {
                    // Use the new dataCache utility if available
                    if (window.dataCache) {
                        window.dataCache.set(cacheKey, data, 300); // Cache for 5 minutes
                    } else {
                        // Fallback to localStorage
                        localStorage.setItem(cacheKey, JSON.stringify({
                            data,
                            timestamp: Date.now()
                        }));
                    }
                }
                
                return data;
            };

            // Fetch all data in parallel for speed
            const [customersData, quotationsData, invoicesData, contractsData, leadsData] = await Promise.all([
                fetchWithAuth(window.getApiUrl('/customer'), true).catch(() => ({ items: [] })),
                fetchWithAuth(window.getApiUrl('/quotation'), true).catch(() => ({ items: [] })),
                fetchWithAuth(window.getApiUrl('/invoice'), true).catch(() => ({ items: [] })),
                fetchWithAuth(window.getApiUrl('/contract'), true).catch(() => ({ items: [] })),
                fetchWithAuth(window.getApiUrl('/lead'), true).catch(() => ({ items: [] })),
            ]);

            // Process data with better filtering and calculations
            const activeQuotations = (quotationsData.items || []).filter(q =>
                q && q.objectData && ['sent', 'draft'].includes(q.objectData.status)
            );
            
            const pendingInvoices = (invoicesData.items || []).filter(i =>
                i && i.objectData && ['sent', 'overdue'].includes(i.objectData.status)
            );
            
            const activeContracts = (contractsData.items || []).filter(c =>
                c && c.objectData && ['signed', 'active'].includes(c.objectData.status)
            );
            
            const activeLeads = (leadsData.items || []).filter(l =>
                l && l.objectData && ['new', 'contacted', 'qualified'].includes(l.objectData.status)
            );

            // Calculate revenue
            const totalRevenue = (invoicesData.items || [])
                .filter(i => i && i.objectData && i.objectData.status === 'paid')
                .reduce((sum, i) => sum + (parseFloat(i.objectData.total_amount) || 0), 0);

            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            const monthlyRevenue = (invoicesData.items || [])
                .filter(i => {
                    if (!i || !i.objectData || i.objectData.status !== 'paid' || !i.objectData.paid_date) return false;
                    const paidDate = new Date(i.objectData.paid_date);
                    return paidDate.getMonth() === currentMonth && paidDate.getFullYear() === currentYear;
                })
                .reduce((sum, i) => sum + (parseFloat(i.objectData.total_amount) || 0), 0);

            // Generate upcoming tasks
            const upcomingTasks = [];
            
            // Overdue invoices
            (invoicesData.items || []).forEach(invoice => {
                if (invoice && invoice.objectData && invoice.objectData.due_date) {
                    const dueDate = new Date(invoice.objectData.due_date);
                    const today = new Date();
                    if (dueDate < today && invoice.objectData.status !== 'paid') {
                        upcomingTasks.push({
                            type: 'overdue_invoice',
                            title: `Overdue Invoice #${invoice.objectId.substring(0, 8)}`,
                            description: `Due ${formatDate(invoice.objectData.due_date)}`,
                            priority: 'high',
                            dueDate: invoice.objectData.due_date,
                            id: invoice.objectId
                        });
                    }
                }
            });
            
            // Expiring quotations
            (quotationsData.items || []).forEach(quotation => {
                if (quotation && quotation.objectData && quotation.objectData.valid_until) {
                    const validUntil = new Date(quotation.objectData.valid_until);
                    const today = new Date();
                    const daysUntilExpiry = Math.ceil((validUntil - today) / (1000 * 60 * 60 * 24));
                    if (daysUntilExpiry <= 7 && daysUntilExpiry > 0 && quotation.objectData.status === 'sent') {
                        upcomingTasks.push({
                            type: 'expiring_quotation',
                            title: `Quotation Expiring Soon #${quotation.objectId.substring(0, 8)}`,
                            description: `Expires in ${daysUntilExpiry} days`,
                            priority: 'medium',
                            dueDate: quotation.objectData.valid_until,
                            id: quotation.objectId
                        });
                    }
                }
            });
            
            // Follow-up leads
            (leadsData.items || []).forEach(lead => {
                if (lead && lead.objectData && lead.objectData.last_contact) {
                    const lastContact = new Date(lead.objectData.last_contact);
                    const today = new Date();
                    const daysSinceContact = Math.ceil((today - lastContact) / (1000 * 60 * 60 * 24));
                    if (daysSinceContact >= 7 && ['contacted', 'qualified'].includes(lead.objectData.status)) {
                        upcomingTasks.push({
                            type: 'follow_up_lead',
                            title: `Follow up with ${lead.objectData.name}`,
                            description: `Last contacted ${daysSinceContact} days ago`,
                            priority: 'medium',
                            dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                            id: lead.objectId
                        });
                    }
                }
            });
            
            // Sort tasks by priority and due date
            upcomingTasks.sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                }
                return new Date(a.dueDate) - new Date(b.dueDate);
            });

            // Generate recent activities from all data
            const allActivities = [];
            (customersData.items || []).slice(0, 5).forEach(customer => {
                allActivities.push({
                    type: 'customer',
                    description: `New customer added: ${customer && customer.objectData && customer.objectData.name ? customer.objectData.name : 'Unknown'}`,
                    time: customer && customer.createdAt ? customer.createdAt : null,
                    id: customer && customer.objectId ? customer.objectId : null
                });
            });
            (quotationsData.items || []).slice(0, 5).forEach(quotation => {
                allActivities.push({
                    type: 'quotation',
                    description: `Quotation ${quotation && quotation.objectData && quotation.objectData.status ? quotation.objectData.status : 'unknown'}: #${quotation && quotation.objectId ? quotation.objectId.substring(0, 8) : 'unknown'}`,
                    time: quotation && quotation.updatedAt ? quotation.updatedAt : (quotation && quotation.createdAt ? quotation.createdAt : null),
                    id: quotation && quotation.objectId ? quotation.objectId : null
                });
            });
            (invoicesData.items || []).slice(0, 5).forEach(invoice => {
                allActivities.push({
                    type: 'invoice',
                    description: `Invoice ${invoice && invoice.objectData && invoice.objectData.status ? invoice.objectData.status : 'unknown'}: #${invoice && invoice.objectId ? invoice.objectId.substring(0, 8) : 'unknown'}`,
                    time: invoice && invoice.updatedAt ? invoice.updatedAt : (invoice && invoice.createdAt ? invoice.createdAt : null),
                    id: invoice && invoice.objectId ? invoice.objectId : null
                });
            });
            allActivities.sort((a, b) => new Date(b.time) - new Date(a.time));

            setStats({
                totalCustomers: (customersData.items || []).length,
                activeQuotations: activeQuotations.length,
                pendingInvoices: pendingInvoices.length,
                activeContracts: activeContracts.length,
                totalRevenue: totalRevenue,
                monthlyRevenue: monthlyRevenue,
                activeLeads: activeLeads.length,
                recentActivities: allActivities.slice(0, 10),
                upcomingTasks: upcomingTasks.slice(0, 5),
                chartData: {
                    revenue: monthlyRevenue,
                    customers: (customersData.items || []).length,
                    invoices: (invoicesData.items || []).length,
                    quotations: (quotationsData.items || []).length
                }
            });
            
            setLastUpdated(new Date());
        } catch (error) {
            setError(error.message || 'Failed to load dashboard data.');
            // If it's an authentication error, redirect to login using our improved routing
            if (error.message && (error.message.toLowerCase().includes('401') || error.message.toLowerCase().includes('unauthorized'))) {
                localStorage.removeItem('authToken');
                // Use the app-navigate event for consistent routing
                window.dispatchEvent(new CustomEvent('app-navigate', { 
                    detail: { 
                        page: 'login',
                        id: null,
                        action: null,
                        params: { redirect: 'dashboard' }
                    } 
                }));
            }
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Auto-refresh functionality
    React.useEffect(() => {
        if (autoRefresh && isAuthenticated) {
            refreshIntervalRef.current = setInterval(() => {
                fetchDashboardData(true);
            }, 5 * 60 * 1000); // Refresh every 5 minutes
            
            return () => {
                if (refreshIntervalRef.current) {
                    clearInterval(refreshIntervalRef.current);
                }
            };
        }
    }, [autoRefresh, isAuthenticated]);

    React.useEffect(() => {
        // Only fetch data if authenticated
        if (isAuthenticated && (token || localStorage.getItem('authToken'))) {
            fetchDashboardData();
        }

        // Cleanup on unmount
        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
    }, [isAuthenticated, token]);

    // Fetch subscription data using enhanced API
    React.useEffect(() => {
        const fetchSubscription = async () => {
            try {
                // Only fetch if component is actually being rendered and user is authenticated
                if (isAuthenticated && token && authContext && authContext.isAuthenticated) {
                    const response = await fetch(window.getApiUrl('/subscription-management/current'), {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.data) {
                            setSubscription(data.data);
                        }
                    }
                }
            } catch (error) {
                console.error('Error fetching subscription:', error);
            }
        };

        // Only run if we have proper auth context
        if (authContext && authContext.isAuthenticated) {
            fetchSubscription();
        }
    }, [isAuthenticated, token, authContext]);

    // Manual refresh function
    const handleRefresh = () => {
        fetchDashboardData(true);
    };

    // Toggle auto-refresh
    const toggleAutoRefresh = () => {
        setAutoRefresh(!autoRefresh);
    };

    const handleQuickAction = (action) => {
        switch(action) {
            case 'add-customer':
                navigate('customers', null, 'new');
                break;
            case 'create-quotation':
                navigate('quotations', null, 'new');
                break;
            case 'generate-invoice':
                navigate('invoices', null, 'new');
                break;
            case 'new-contract':
                navigate('contracts', null, 'new');
                break;
            case 'view-reports':
                navigate('reports');
                break;
            default:
                break;
        }
    };

    if (loading) {
        // Use the global LoadingSpinner if available, otherwise use our inline component
        const LoadingComponent = window.LoadingSpinner || InlineLoadingSpinner;
        
        return (
            <div className="h-64 flex items-center justify-center">
                <LoadingComponent 
                    size="lg" 
                    text="Loading dashboard data..." 
                />
            </div>
        );
    }

    if (error) {
        // Use the global ErrorMessage if available, otherwise use our inline component
        const ErrorComponent = window.ErrorMessage || InlineErrorMessage;
        
        return (
            <ErrorComponent 
                error={error} 
                onRetry={() => fetchDashboardData()} 
            />
        );
    }



    return (
        <div data-name="dashboard-page" className="p-6">
            {/* Trial Banner */}
            {/* Enhanced Trial Status Banner */}
            <TrialStatusBanner
                authContext={authContext}
                onUpgrade={() => {
                    // Navigate to subscriptions page or show upgrade modal
                    window.location.hash = '#/subscriptions';
                }}
            />
            
            {/* Welcome Header with Controls */}
            <div className="mb-8 flex justify-between items-start">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                        Welcome back{user && user.name ? `, ${user.name}` : ''}!
                    </h1>
                    <p className="text-gray-600 mt-1">Here's what's happening with your business today.</p>
                    {lastUpdated && (
                        <p className="text-sm text-gray-500 mt-2">
                            Last updated: {lastUpdated.toLocaleTimeString()}
                        </p>
                    )}
                </div>
                <div className="flex items-center space-x-3">
                    <button
                        onClick={toggleAutoRefresh}
                        className={`px-3 py-2 text-sm rounded-md border ${
                            autoRefresh 
                                ? 'bg-green-50 text-green-700 border-green-200' 
                                : 'bg-gray-50 text-gray-700 border-gray-200'
                        }`}
                        title={autoRefresh ? 'Auto-refresh enabled' : 'Auto-refresh disabled'}
                    >
                        <i className={`fas ${autoRefresh ? 'fa-toggle-on' : 'fa-toggle-off'} mr-1`}></i>
                        Auto-refresh
                    </button>
                    <button
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <i className={`fas fa-sync-alt mr-2 ${refreshing ? 'fa-spin' : ''}`}></i>
                        Refresh
                    </button>
                </div>
            </div>
            {/* Subscription Status Section */}
            <div className="mb-8">
                <SubscriptionStatus
                    authContext={authContext}
                    subscription={subscription}
                    onUpgrade={() => {
                        window.location.hash = '#/subscriptions';
                    }}
                    onManage={() => {
                        setShowSubscriptionModal(true);
                    }}
                />
            </div>

            {/* Enhanced Stats Grid */}
            <div data-name="stats-grid" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Revenue Card */}
                <div data-name="stats-card-revenue" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Monthly Revenue</p>
                            <h3 className="text-3xl font-bold text-gray-900 mt-2">{formatCurrency(stats.monthlyRevenue)}</h3>
                            <p className="text-sm text-green-600 mt-1">
                                <i className="fas fa-arrow-up mr-1"></i>
                                Total: {formatCurrency(stats.totalRevenue)}
                            </p>
                        </div>
                        <div className="p-4 bg-green-100 rounded-full">
                            <i className="fas fa-rupee-sign text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                {/* ...existing code... */}
                <div data-name="stats-card-customers" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Customers</p>
                            <h3 className="text-3xl font-bold text-gray-900 mt-2">{stats.totalCustomers}</h3>
                            <p className="text-sm text-green-600 mt-1">
                                <i className="fas fa-arrow-up mr-1"></i>
                                +12% from last month
                            </p>
                        </div>
                        <div className="p-4 bg-blue-100 rounded-full">
                            <i className="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                {/* ...existing code for other stat cards... */}
                <div data-name="stats-card-quotations" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Active Quotations</p>
                            <h3 className="text-3xl font-bold text-gray-900 mt-2">{stats.activeQuotations}</h3>
                            <p className="text-sm text-green-600 mt-1">
                                <i className="fas fa-arrow-up mr-1"></i>
                                +8% from last month
                            </p>
                        </div>
                        <div className="p-4 bg-green-100 rounded-full">
                            <i className="fas fa-file-invoice-dollar text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                <div data-name="stats-card-invoices" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Pending Invoices</p>
                            <h3 className="text-3xl font-bold text-gray-900 mt-2">{stats.pendingInvoices}</h3>
                            <p className="text-sm text-yellow-600 mt-1">
                                <i className="fas fa-exclamation-triangle mr-1"></i>
                                Needs attention
                            </p>
                        </div>
                        <div className="p-4 bg-yellow-100 rounded-full">
                            <i className="fas fa-file-invoice text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                <div data-name="stats-card-contracts" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">Active Contracts</p>
                            <h3 className="text-3xl font-bold text-gray-900 mt-2">{stats.activeContracts}</h3>
                            <p className="text-sm text-blue-600 mt-1">
                                <i className="fas fa-check-circle mr-1"></i>
                                All up to date
                            </p>
                        </div>
                        <div className="p-4 bg-purple-100 rounded-full">
                            <i className="fas fa-file-contract text-purple-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div data-name="dashboard-content" className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div data-name="recent-activities" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-semibold text-gray-900">Recent Activities</h2>
                        <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                            View All
                        </button>
                    </div>
                    {stats.recentActivities.length === 0 ? (
                        <div className="text-center py-8">
                            <i className="fas fa-clock text-gray-300 text-3xl mb-3"></i>
                            <p className="text-gray-500">No recent activities</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {stats.recentActivities.map((activity, index) => (
                                <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                                    <div className="flex items-center space-x-3">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                            activity.type === 'customer' ? 'bg-blue-100' :
                                            activity.type === 'quotation' ? 'bg-green-100' :
                                            activity.type === 'invoice' ? 'bg-yellow-100' : 'bg-purple-100'
                                        }`}>
                                            <i className={`fas ${
                                                activity.type === 'customer' ? 'fa-user text-blue-600' :
                                                activity.type === 'quotation' ? 'fa-file-invoice-dollar text-green-600' :
                                                activity.type === 'invoice' ? 'fa-file-invoice text-yellow-600' : 'fa-file-contract text-purple-600'
                                            } text-sm`}></i>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                                            <p className="text-xs text-gray-500">{formatDateTime(activity.time)}</p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
                <div data-name="quick-actions" className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
                    <div className="grid grid-cols-2 gap-4">
                        <button
                            data-name="add-customer-button"
                            className="p-6 border-2 border-dashed border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all group"
                            onClick={() => handleQuickAction('add-customer')}
                        >
                            <i className="fas fa-user-plus text-2xl mb-3 text-blue-500 group-hover:text-blue-600"></i>
                            <p className="font-medium text-gray-700 group-hover:text-blue-600">Add Customer</p>
                            <p className="text-xs text-gray-500 mt-1">Create new customer profile</p>
                        </button>
                        <button
                            data-name="create-quotation-button"
                            className="p-6 border-2 border-dashed border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all group"
                            onClick={() => handleQuickAction('create-quotation')}
                        >
                            <i className="fas fa-file-invoice-dollar text-2xl mb-3 text-green-500 group-hover:text-green-600"></i>
                            <p className="font-medium text-gray-700 group-hover:text-green-600">Create Quotation</p>
                            <p className="text-xs text-gray-500 mt-1">Generate price quote</p>
                        </button>
                        <button
                            data-name="generate-invoice-button"
                            className="p-6 border-2 border-dashed border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-all group"
                            onClick={() => handleQuickAction('generate-invoice')}
                        >
                            <i className="fas fa-file-invoice text-2xl mb-3 text-yellow-500 group-hover:text-yellow-600"></i>
                            <p className="font-medium text-gray-700 group-hover:text-yellow-600">Generate Invoice</p>
                            <p className="text-xs text-gray-500 mt-1">Create billing invoice</p>
                        </button>
                        <button
                            data-name="new-contract-button"
                            className="p-6 border-2 border-dashed border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all group"
                            onClick={() => handleQuickAction('new-contract')}
                        >
                            <i className="fas fa-file-contract text-2xl mb-3 text-purple-500 group-hover:text-purple-600"></i>
                            <p className="font-medium text-gray-700 group-hover:text-purple-600">New Contract</p>
                            <p className="text-xs text-gray-500 mt-1">Draft service agreement</p>
                        </button>
                    </div>
                </div>
            </div>

            {/* Upcoming Tasks Section */}
            {stats.upcomingTasks && stats.upcomingTasks.length > 0 && (
                <div className="mt-8">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div className="flex items-center justify-between mb-6">
                            <h2 className="text-xl font-semibold text-gray-900">
                                <i className="fas fa-tasks mr-2 text-orange-500"></i>
                                Upcoming Tasks
                            </h2>
                            <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {stats.upcomingTasks.length} pending
                            </span>
                        </div>
                        <div className="space-y-4">
                            {stats.upcomingTasks.map((task, index) => (
                                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                    <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                                        task.priority === 'high' ? 'bg-red-500' :
                                        task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                                    }`}></div>
                                    <div className="flex-1 min-w-0">
                                        <p className="text-sm font-medium text-gray-900">{task.title}</p>
                                        <p className="text-sm text-gray-600">{task.description}</p>
                                        <div className="flex items-center mt-1 space-x-4">
                                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                                task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                                task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                                            }`}>
                                                {task.priority} priority
                                            </span>
                                            <span className="text-xs text-gray-500">
                                                <i className="fas fa-clock mr-1"></i>
                                                {formatDate(task.dueDate)}
                                            </span>
                                        </div>
                                    </div>
                                    <button 
                                        className="flex-shrink-0 text-gray-400 hover:text-gray-600"
                                        onClick={() => {
                                            if (task.type === 'overdue_invoice') navigate('invoices');
                                            else if (task.type === 'expiring_quotation') navigate('quotations');
                                            else if (task.type === 'follow_up_lead') navigate('leads');
                                        }}
                                    >
                                        <i className="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Subscription Management Modal */}
            {showSubscriptionModal && (
                <SubscriptionManagementModal
                    isOpen={showSubscriptionModal}
                    onClose={() => setShowSubscriptionModal(false)}
                    subscription={subscription}
                    authContext={authContext}
                />
            )}
        </div>
    );
}
