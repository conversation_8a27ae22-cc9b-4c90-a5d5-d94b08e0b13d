/**
 * Emergency fix for missing utility components
 * This script creates fallbacks for critical components
 */

// Create LoadingSpinner if it doesn't exist
if (typeof window.LoadingSpinner === 'undefined') {
    console.warn('Creating LoadingSpinner fallback');
    window.LoadingSpinner = function({ size = 'md', text = 'Loading...', center = true }) {
        const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-12 w-12' : 'h-8 w-8';
        const centerClass = center ? 'flex items-center justify-center' : '';
        
        return React.createElement('div', { 
            className: `flex flex-col items-center ${centerClass}` 
        }, [
            React.createElement('div', { 
                key: 'spinner',
                className: `animate-spin rounded-full ${sizeClass} border-b-2 border-blue-600`
            }),
            text && React.createElement('p', { 
                key: 'text',
                className: 'mt-2 text-gray-600' 
            }, text)
        ]);
    };
}

// Create ErrorMessage if it doesn't exist
if (typeof window.ErrorMessage === 'undefined') {
    console.warn('Creating ErrorMessage fallback');
    window.ErrorMessage = function({ error, onRetry }) {
        const message = typeof error === 'string' ? error : (error?.message || 'An error occurred');
        
        return React.createElement('div', { 
            className: 'bg-red-50 border border-red-200 rounded-md p-4 my-4' 
        }, [
            React.createElement('p', { 
                key: 'message',
                className: 'text-red-700' 
            }, message),
            onRetry && React.createElement('button', { 
                key: 'retry',
                onClick: onRetry,
                className: 'mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200'
            }, 'Try Again')
        ]);
    };
}

// Create NotificationContainer if it doesn't exist
if (typeof window.NotificationContainer === 'undefined') {
    console.warn('Creating NotificationContainer fallback');
    window.NotificationContainer = function() {
        return React.createElement('div', { 
            id: 'notification-container',
            className: 'fixed top-4 right-4 z-50 flex flex-col gap-2'
        });
    };
}

// Create showNotification if it doesn't exist
if (typeof window.showNotification === 'undefined') {
    console.warn('Creating showNotification fallback');
    window.showNotification = function({ type = 'info', message, duration = 5000 }) {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // Create a simple alert for critical notifications
        if (type === 'error' || type === 'warning') {
            setTimeout(() => alert(`${type.toUpperCase()}: ${message}`), 100);
        }
    };
}

console.log('Emergency component fallbacks loaded successfully');