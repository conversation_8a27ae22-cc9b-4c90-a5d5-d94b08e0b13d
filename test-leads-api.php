<?php
/**
 * Test script to debug leads API issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/api/db-config.php';

try {
    echo "Testing Leads API...\n\n";
    
    // 1. Check database connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    echo "✅ Database connection successful\n";
    
    // 2. Check if leads table exists
    $result = $conn->query("SHOW TABLES LIKE 'leads'");
    if ($result->num_rows > 0) {
        echo "✅ Leads table exists\n";
        
        // Check table structure
        $result = $conn->query("DESCRIBE leads");
        echo "\nLeads table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
        }
        
        // Check if there are any leads
        $result = $conn->query("SELECT COUNT(*) as count FROM leads");
        $row = $result->fetch_assoc();
        echo "\nTotal leads in database: " . $row['count'] . "\n";
        
    } else {
        echo "❌ Leads table does not exist\n";
        echo "Creating leads table...\n";
        
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS leads (
            object_id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) NOT NULL,
            company_id VARCHAR(36),
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(50),
            company VARCHAR(255),
            source VARCHAR(100),
            status VARCHAR(50) DEFAULT 'new',
            priority VARCHAR(20) DEFAULT 'medium',
            value DECIMAL(10,2) DEFAULT 0.00,
            notes TEXT,
            tags JSON,
            custom_fields JSON,
            assigned_to VARCHAR(36),
            last_contact_date DATETIME,
            next_follow_up DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_company_id (company_id),
            INDEX idx_status (status),
            INDEX idx_assigned_to (assigned_to),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createTableSQL)) {
            echo "✅ Leads table created successfully\n";
        } else {
            throw new Exception("Failed to create leads table: " . $conn->error);
        }
    }
    
    // 3. Test lead creation
    echo "\n--- Testing Lead Creation ---\n";
    
    // Sample lead data
    $leadData = [
        'object_id' => 'test-lead-' . uniqid(),
        'user_id' => 'test-user-123',
        'name' => 'Test Lead',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'company' => 'Test Company',
        'source' => 'website',
        'status' => 'new',
        'priority' => 'medium',
        'value' => 1000.00,
        'notes' => 'This is a test lead'
    ];
    
    // Prepare SQL
    $sql = "INSERT INTO leads (object_id, user_id, name, email, phone, company, source, status, priority, value, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }
    
    $stmt->bind_param("sssssssssds", 
        $leadData['object_id'],
        $leadData['user_id'],
        $leadData['name'],
        $leadData['email'],
        $leadData['phone'],
        $leadData['company'],
        $leadData['source'],
        $leadData['status'],
        $leadData['priority'],
        $leadData['value'],
        $leadData['notes']
    );
    
    if ($stmt->execute()) {
        echo "✅ Test lead created successfully\n";
        echo "Lead ID: " . $leadData['object_id'] . "\n";
        
        // Clean up test data
        $conn->query("DELETE FROM leads WHERE object_id = '" . $leadData['object_id'] . "'");
        echo "✅ Test lead cleaned up\n";
    } else {
        throw new Exception("Failed to create test lead: " . $stmt->error);
    }
    
    // 4. Test API endpoint
    echo "\n--- Testing API Endpoint ---\n";
    
    // Simulate API call
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/biz/api/api.php/lead';
    
    // Test data
    $testData = json_encode([
        'name' => 'API Test Lead',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'company' => 'API Test Company',
        'source' => 'api_test'
    ]);
    
    echo "Test data: " . $testData . "\n";
    
    // Include the API handler
    ob_start();
    
    // Mock the input
    $GLOBALS['HTTP_RAW_POST_DATA'] = $testData;
    
    try {
        include_once __DIR__ . '/api/handlers/crud-handler.php';
        echo "✅ CRUD handler loaded successfully\n";
    } catch (Exception $e) {
        echo "❌ Error loading CRUD handler: " . $e->getMessage() . "\n";
    }
    
    $output = ob_get_clean();
    if ($output) {
        echo "API Output: " . $output . "\n";
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
