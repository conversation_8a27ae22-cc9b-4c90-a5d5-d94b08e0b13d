function ExtendTrialModal({ subscription, daysToAdd, setDaysToAdd, onCancel, onConfirm, processing }) {
    try {
        return (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Extend Trial Period</h3>
                    
                    <div className="mb-4">
                        <p className="text-sm text-gray-600 mb-2">
                            Extending trial for <span className="font-semibold">{subscription?.company_name}</span>
                        </p>
                        
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Days to add
                        </label>
                        <select
                            value={daysToAdd}
                            onChange={(e) => setDaysToAdd(parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value={7}>7 days</option>
                            <option value={14}>14 days</option>
                            <option value={30}>30 days</option>
                        </select>
                    </div>
                    
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={onCancel}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={onConfirm}
                            disabled={processing}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                        >
                            {processing ? 'Processing...' : 'Extend Trial'}
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ExtendTrialModal component error:', error);
        return null;
    }
}