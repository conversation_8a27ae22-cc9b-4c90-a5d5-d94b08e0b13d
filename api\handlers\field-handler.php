<?php
/**
 * Field Handler
 * Handles field operations and validations
 */

function validateField($fieldName, $value, $fieldType = 'text') {
    switch ($fieldType) {
        case 'email':
            return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
        case 'number':
            return is_numeric($value);
        case 'date':
            return strtotime($value) !== false;
        case 'required':
            return !empty(trim($value));
        default:
            return true;
    }
}

function sanitizeField($value, $fieldType = 'text') {
    switch ($fieldType) {
        case 'email':
            return filter_var($value, FILTER_SANITIZE_EMAIL);
        case 'number':
            return floatval($value);
        case 'int':
            return intval($value);
        case 'text':
        default:
            return htmlspecialchars(strip_tags(trim($value)));
    }
}

function formatFieldForDisplay($value, $fieldType = 'text') {
    switch ($fieldType) {
        case 'date':
            return date('Y-m-d', strtotime($value));
        case 'datetime':
            return date('Y-m-d H:i:s', strtotime($value));
        case 'currency':
            return number_format($value, 2);
        case 'percentage':
            return number_format($value, 2) . '%';
        default:
            return $value;
    }
}

function extractFields($objectType, $objectData) {
    // Define field mappings for each object type
    $fieldMappings = [
        'customer' => [
            'name' => 'name',
            'email' => 'email',
            'phone' => 'phone',
            'company' => 'company',
            'address' => 'address',
            'type' => 'type',
            'notes' => 'notes',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'invoice' => [
            'customer_id' => 'customer',
            'invoice_number' => 'invoiceNumber',
            'subtotal' => 'subtotal',
            'tax' => 'tax',
            'tax_rate' => 'taxRate',
            'discount' => 'discount',
            'total' => 'total',
            'amount_paid' => 'amountPaid',
            'balance' => 'balance',
            'due_date' => 'dueDate',
            'status' => 'status',
            'payment_method' => 'paymentMethod',
            'paid_at' => 'paidAt',
            'notes' => 'notes',
            'terms' => 'terms',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'quotation' => [
            'customer_id' => 'customer',
            'quotation_number' => 'quotationNumber',
            'project_name' => 'projectName',
            'subtotal' => 'subtotal',
            'tax' => 'tax',
            'tax_rate' => 'taxRate',
            'discount' => 'discount',
            'total' => 'total',
            'valid_until' => 'validUntil',
            'status' => 'status',
            'notes' => 'notes',
            'terms' => 'terms',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'contract' => [
            'customer_id' => 'customer',
            'contract_number' => 'contractNumber',
            'title' => 'title',
            'description' => 'description',
            'value' => 'value',
            'status' => 'status',
            'start_date' => 'startDate',
            'end_date' => 'endDate',
            'type' => 'type',
            'scope' => 'scope',
            'payment_terms' => 'paymentTerms',
            'terms' => 'terms',
            'renewal_terms' => 'renewalTerms',
            'is_recurring' => 'isRecurring',
            'billing_cycle' => 'billingCycle',
            'customer_signature' => 'customerSignature',
            'signed_at1' => 'signedAt1',
            'signed_at2' => 'signedAt2',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'lead' => [
            'name' => 'name',
            'email' => 'email',
            'phone' => 'phone',
            'company' => 'company',
            'position' => 'position',
            'source' => 'source',
            'status' => 'status',
            'priority' => 'priority',
            'value' => 'value',
            'notes' => 'notes',
            'assigned_to' => 'assigned_to',
            'next_follow_up' => 'next_follow_up',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'item' => [
            'name' => 'name',
            'description' => 'description',
            'category_id' => 'category',
            'subcategory_id' => 'subcategory',
            'sku' => 'sku',
            'price' => 'price',
            'cost_price' => 'costPrice',
            'tax' => 'tax',
            'stock_quantity' => 'stockQuantity',
            'unit' => 'unit',
            'is_active' => 'isActive',
            'is_recurring' => 'isRecurring',
            'recurring_period' => 'recurringPeriod',
            'item_type' => 'itemType',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'item_category' => [
            'name' => 'name',
            'description' => 'description',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'item_subcategory' => [
            'name' => 'name',
            'description' => 'description',
            'parent_id' => 'parentId',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'settings' => [
            'company_name' => 'companyName',
            'company_email' => 'companyEmail',
            'company_phone' => 'companyPhone',
            'company_address' => 'companyAddress',
            'company_website' => 'companyWebsite',
            'tax_rate' => 'taxRate',
            'currency' => 'currency',
            'date_format' => 'dateFormat',
            'theme' => 'theme',
            'logo' => 'logo',
            'signature' => 'signature',
            'company_gst' => 'companyGST',
            'authorized_name' => 'authorizedName',
            'bank_details' => 'bankDetails',
            'upi_id' => 'upiId',
            'default_payment_terms' => 'defaultPaymentTerms',
            'default_notes' => 'defaultNotes',
            'notifications' => 'notifications',
            'templates' => 'templates',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at'
        ],
        'company' => [
            'name' => 'name',
            'email' => 'email',
            'phone' => 'phone',
            'address' => 'address',
            'website' => 'website',
            'owner_id' => 'ownerId',
            'members' => 'members',
            'subscription_id' => 'subscriptionId',
            'status' => 'status',
            'created_at' => 'createdAt',
            'updated_at' => 'updatedAt'
        ]
    ];

    $fields = [];
    $mapping = $fieldMappings[$objectType] ?? [];

    foreach ($mapping as $dbField => $dataField) {
        if (isset($objectData[$dataField])) {
            // Handle JSON fields that need to be encoded
            if (in_array($dbField, ['notifications', 'templates']) && is_array($objectData[$dataField])) {
                $fields[$dbField] = json_encode($objectData[$dataField]);
            } else {
                $fields[$dbField] = $objectData[$dataField];
            }
        }
    }

    // Add timestamps if not provided
    if (!isset($fields['created_at'])) {
        $fields['created_at'] = date('Y-m-d H:i:s');
    }
    if (!isset($fields['updated_at'])) {
        $fields['updated_at'] = date('Y-m-d H:i:s');
    }

    return $fields;
}

function getFieldSchema($objectType) {
    $schemas = [
        'customer' => [
            'name' => ['type' => 'text', 'required' => true],
            'email' => ['type' => 'email', 'required' => true],
            'phone' => ['type' => 'text', 'required' => false],
            'address' => ['type' => 'text', 'required' => false]
        ],
        'invoice' => [
            'customer_id' => ['type' => 'text', 'required' => true],
            'amount' => ['type' => 'number', 'required' => true],
            'due_date' => ['type' => 'date', 'required' => true],
            'status' => ['type' => 'text', 'required' => true]
        ],
        'quotation' => [
            'customer_id' => ['type' => 'text', 'required' => true],
            'amount' => ['type' => 'number', 'required' => true],
            'valid_until' => ['type' => 'date', 'required' => true],
            'status' => ['type' => 'text', 'required' => true]
        ],
        'lead' => [
            'name' => ['type' => 'text', 'required' => true],
            'email' => ['type' => 'email', 'required' => false],
            'phone' => ['type' => 'text', 'required' => false],
            'status' => ['type' => 'text', 'required' => true],
            'source' => ['type' => 'text', 'required' => false]
        ],
        'contract' => [
            'customer_id' => ['type' => 'text', 'required' => true],
            'title' => ['type' => 'text', 'required' => true],
            'value' => ['type' => 'number', 'required' => true],
            'start_date' => ['type' => 'date', 'required' => true],
            'end_date' => ['type' => 'date', 'required' => true]
        ],
        'item' => [
            'name' => ['type' => 'text', 'required' => true],
            'description' => ['type' => 'text', 'required' => false],
            'price' => ['type' => 'number', 'required' => true],
            'category' => ['type' => 'text', 'required' => false]
        ]
    ];
    
    return isset($schemas[$objectType]) ? $schemas[$objectType] : [];
}

function validateObjectData($objectType, $data) {
    $schema = getFieldSchema($objectType);
    $errors = [];
    
    foreach ($schema as $fieldName => $fieldConfig) {
        $value = isset($data[$fieldName]) ? $data[$fieldName] : '';
        
        // Check required fields
        if ($fieldConfig['required'] && empty(trim($value))) {
            $errors[$fieldName] = "Field '$fieldName' is required";
            continue;
        }
        
        // Validate field type if value is not empty
        if (!empty($value) && !validateField($fieldName, $value, $fieldConfig['type'])) {
            $errors[$fieldName] = "Field '$fieldName' has invalid format for type '{$fieldConfig['type']}'";
        }
    }
    
    return $errors;
}

function sanitizeObjectData($objectType, $data) {
    $schema = getFieldSchema($objectType);
    $sanitized = [];
    
    foreach ($data as $fieldName => $value) {
        if (isset($schema[$fieldName])) {
            $sanitized[$fieldName] = sanitizeField($value, $schema[$fieldName]['type']);
        } else {
            // For fields not in schema, apply basic sanitization
            $sanitized[$fieldName] = sanitizeField($value);
        }
    }
    
    return $sanitized;
}
?>
