# Disable directory browsing
Options -Indexes

# Deny access to sensitive files
<FilesMatch "\.(log|ini|config|env|htaccess|htpasswd|json|sql)$">
Order allow,deny
Deny from all
</FilesMatch>

# Allow access to API entry points
<FilesMatch "^(api\.php|auth-handler\.php|register\.php)$">
Order deny,allow
Allow from all
</FilesMatch>

# Set PHP handling
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log ../logs/api_errors.log
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value memory_limit 256M
    php_value max_execution_time 60
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Content-Security-Policy "default-src 'self'"
</IfModule>

# CORS headers for API
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>

# Handle OPTIONS requests for CORS preflight
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]