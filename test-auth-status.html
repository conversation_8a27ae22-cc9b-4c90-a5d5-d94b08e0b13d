<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Status Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="config.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Authentication Status Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Authentication Status -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">Authentication Status</h2>
                <div id="auth-status" class="space-y-2">
                    <div class="text-gray-600">Checking authentication...</div>
                </div>
                <button onclick="checkAuth()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Refresh Auth Status
                </button>
            </div>

            <!-- Token Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">Token Information</h2>
                <div id="token-info" class="space-y-2">
                    <div class="text-gray-600">Checking token...</div>
                </div>
                <button onclick="checkToken()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Check Token
                </button>
            </div>

            <!-- API Test -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">API Test</h2>
                <div id="api-test" class="space-y-2">
                    <div class="text-gray-600">Ready to test APIs...</div>
                </div>
                <button onclick="testAPIs()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test APIs
                </button>
            </div>

            <!-- Login Form -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold mb-4">Quick Login</h2>
                <div class="space-y-4">
                    <input type="email" id="email" placeholder="Email" class="w-full px-3 py-2 border rounded">
                    <input type="password" id="password" placeholder="Password" class="w-full px-3 py-2 border rounded">
                    <button onclick="quickLogin()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        Login
                    </button>
                </div>
                <div id="login-result" class="mt-4"></div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Detailed Results</h2>
            <pre id="detailed-results" class="bg-gray-50 p-4 rounded text-sm overflow-auto max-h-96">
No tests run yet.
            </pre>
        </div>
    </div>

    <script>
        function updateResults(message) {
            const resultsDiv = document.getElementById('detailed-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.textContent += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function checkAuth() {
            const authDiv = document.getElementById('auth-status');
            
            // Check localStorage
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');
            
            let html = '';
            
            if (token) {
                html += '<div class="text-green-600">✅ Auth token found</div>';
                html += '<div class="text-sm text-gray-600">Token: ' + token.substring(0, 20) + '...</div>';
            } else {
                html += '<div class="text-red-600">❌ No auth token found</div>';
            }
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    html += '<div class="text-green-600">✅ User data found</div>';
                    html += '<div class="text-sm text-gray-600">Email: ' + userData.email + '</div>';
                    html += '<div class="text-sm text-gray-600">Role: ' + userData.role + '</div>';
                } catch (e) {
                    html += '<div class="text-yellow-600">⚠️ Invalid user data</div>';
                }
            } else {
                html += '<div class="text-red-600">❌ No user data found</div>';
            }
            
            authDiv.innerHTML = html;
            updateResults('Authentication check completed');
        }

        async function checkToken() {
            const tokenDiv = document.getElementById('token-info');
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                tokenDiv.innerHTML = '<div class="text-red-600">❌ No token to verify</div>';
                return;
            }
            
            try {
                tokenDiv.innerHTML = '<div class="text-blue-600">🔄 Verifying token...</div>';
                
                const response = await fetch('/biz/api/verify-token.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    tokenDiv.innerHTML = `
                        <div class="text-green-600">✅ Token is valid</div>
                        <div class="text-sm text-gray-600">User: ${data.user.email}</div>
                        <div class="text-sm text-gray-600">Expires: ${new Date(data.user.token_expires).toLocaleString()}</div>
                    `;
                } else {
                    tokenDiv.innerHTML = `<div class="text-red-600">❌ Token invalid: ${data.message || 'Unknown error'}</div>`;
                }
                
                updateResults(`Token verification: ${response.status} - ${JSON.stringify(data)}`);
                
            } catch (error) {
                tokenDiv.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
                updateResults(`Token verification error: ${error.message}`);
            }
        }

        async function testAPIs() {
            const apiDiv = document.getElementById('api-test');
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                apiDiv.innerHTML = '<div class="text-red-600">❌ No token for API testing</div>';
                return;
            }
            
            apiDiv.innerHTML = '<div class="text-blue-600">🔄 Testing APIs...</div>';
            
            const apis = [
                '/biz/api/api.php/subscription-management/current',
                '/biz/api/api.php/subscription-management/trial-status',
                '/biz/api/business-types.php'
            ];
            
            let results = [];
            
            for (const api of apis) {
                try {
                    const response = await fetch(api, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const data = await response.text();
                    results.push({
                        api,
                        status: response.status,
                        success: response.ok,
                        data: data.substring(0, 100) + (data.length > 100 ? '...' : '')
                    });
                    
                    updateResults(`API ${api}: ${response.status} - ${data.substring(0, 200)}`);
                    
                } catch (error) {
                    results.push({
                        api,
                        status: 'Error',
                        success: false,
                        data: error.message
                    });
                    
                    updateResults(`API ${api}: Error - ${error.message}`);
                }
            }
            
            let html = '';
            results.forEach(result => {
                html += `
                    <div class="mb-2 p-2 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}">
                        <div class="font-medium">${result.api}</div>
                        <div class="text-sm">Status: ${result.status}</div>
                        <div class="text-xs text-gray-600">${result.data}</div>
                    </div>
                `;
            });
            
            apiDiv.innerHTML = html;
        }

        async function quickLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('login-result');
            
            if (!email || !password) {
                resultDiv.innerHTML = '<div class="text-red-600">Please enter email and password</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="text-blue-600">🔄 Logging in...</div>';
                
                const response = await fetch('/biz/api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    resultDiv.innerHTML = '<div class="text-green-600">✅ Login successful!</div>';
                    
                    // Refresh auth status
                    checkAuth();
                    
                } else {
                    resultDiv.innerHTML = `<div class="text-red-600">❌ Login failed: ${data.message || 'Unknown error'}</div>`;
                }
                
                updateResults(`Login attempt: ${response.status} - ${JSON.stringify(data)}`);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
                updateResults(`Login error: ${error.message}`);
            }
        }

        // Auto-run auth check on page load
        window.addEventListener('load', () => {
            checkAuth();
            updateResults('Page loaded, authentication status checked');
        });
    </script>
</body>
</html>
