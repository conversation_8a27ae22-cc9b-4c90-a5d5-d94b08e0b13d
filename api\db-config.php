<?php
// Include configuration management
require_once __DIR__ . '/../config/config.php';

// Get database configuration from environment
$dbConfig = Config::getDatabase();

// Validate required database configuration
if (!$dbConfig['username'] || !$dbConfig['database']) {
    if (Config::isDebug()) {
        die("Database configuration missing. Please check your .env file.");
    } else {
        die("Database configuration error.");
    }
}

// Create connection
$conn = new mysqli(
    $dbConfig['host'],
    $dbConfig['username'],
    $dbConfig['password'],
    $dbConfig['database']
);

// Check connection
if ($conn->connect_error) {
    if (Config::isDebug()) {
        die("Connection failed: " . $conn->connect_error);
    } else {
        error_log("Database connection failed: " . $conn->connect_error);
        die("Database connection error.");
    }
}

// Set character set to UTF-8
$conn->set_charset($dbConfig['charset']);

// Helper function to generate unique IDs
function generateId() {
    return 'id_' . uniqid();
}

// Helper function to sanitize input data
function sanitizeInput($data) {
    global $conn;
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    // Handle null values
    if ($data === null) {
        return '';
    }
    
    // Convert to string if not already
    if (!is_string($data)) {
        $data = (string)$data;
    }
    
    return $conn->real_escape_string(trim($data));
}

// Helper function to validate company access
function validateCompanyAccess($companyId, $userId) {
    global $conn;

    if (!$companyId || !$userId) {
        return false;
    }

    $companyId = sanitizeInput($companyId);
    $userId = sanitizeInput($userId);

    // Check if user owns the company or is a member
    $sql = "SELECT id FROM companies WHERE object_id = ? AND (owner_id = ? OR FIND_IN_SET(?, members))";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sss", $companyId, $userId, $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->num_rows > 0;
}

// Helper function to get current user from token
function getCurrentUser($token = null) {
    if (!$token) {
        // Handle getallheaders() not being available in CLI
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
        } else {
            $token = isset($_SERVER['HTTP_AUTHORIZATION']) ? str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']) : null;
        }
    }

    if (!$token) {
        return null;
    }

    // Verify JWT token (simplified - in production use proper JWT library)
    // For now, we'll use a simple token validation
    global $conn;
    $token = sanitizeInput($token);

    $sql = "SELECT * FROM users WHERE auth_token = ? AND token_expires > NOW()";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

// Helper function to convert objects to JSON
function objectToJson($data) {
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Helper function to parse JSON to object
function jsonToObject($json) {
    return json_decode($json, true);
}
