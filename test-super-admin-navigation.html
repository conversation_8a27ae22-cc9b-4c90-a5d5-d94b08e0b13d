<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Super Admin Navigation - Bizma</title>
    <script src="config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Super Admin Navigation Test</h1>
        <p>Test the Super Admin navigation functionality</p>
        
        <div class="test-section">
            <h2>Current State</h2>
            <div id="currentState">Loading...</div>
        </div>
        
        <div class="test-section">
            <h2>Navigation Tests</h2>
            <button onclick="testDirectNavigation()">Test Direct Navigation</button>
            <button onclick="testAppNavigateEvent()">Test App Navigate Event</button>
            <button onclick="testUrlChange()">Test URL Change</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="test-section">
            <h2>Event Log</h2>
            <div id="eventLog" class="log">Waiting for events...</div>
        </div>
        
        <div id="testResult"></div>
    </div>

    <script>
        let eventLog = [];
        
        function logEvent(message, data = null) {
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            const logEntry = `[${timestamp}] ${message}`;
            eventLog.push(logEntry);
            if (data) {
                eventLog.push(`    Data: ${JSON.stringify(data, null, 2)}`);
            }
            updateEventLog();
        }
        
        function updateEventLog() {
            document.getElementById('eventLog').textContent = eventLog.join('\n');
        }
        
        function clearLogs() {
            eventLog = [];
            updateEventLog();
        }
        
        // Monitor navigation events
        window.addEventListener('app-navigate', (event) => {
            logEvent('app-navigate event received', event.detail);
        });
        
        window.addEventListener('popstate', (event) => {
            logEvent('popstate event received', {
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash
            });
        });
        
        // Check current state
        window.addEventListener('load', () => {
            const token = localStorage.getItem('authToken');
            const currentPath = window.location.pathname;
            
            document.getElementById('currentState').innerHTML = `
                <div class="info">
                    <p><strong>Current Path:</strong> ${currentPath}</p>
                    <p><strong>Has Token:</strong> ${!!token}</p>
                    <p><strong>Token Preview:</strong> ${token ? token.substring(0, 20) + '...' : 'None'}</p>
                    <p><strong>Base Path:</strong> ${window.APP_CONFIG.BASE_PATH}</p>
                    <p><strong>API Base URL:</strong> ${window.APP_CONFIG.API_BASE_URL}</p>
                </div>
            `;
            
            logEvent('Page loaded', {
                currentPath,
                hasToken: !!token,
                basePath: window.APP_CONFIG.BASE_PATH
            });
        });
        
        function testDirectNavigation() {
            logEvent('Testing direct navigation to /super-admin');
            const url = window.getAppUrl('/super-admin');
            logEvent('Generated URL', { url });
            window.location.href = url;
        }
        
        function testAppNavigateEvent() {
            logEvent('Testing app-navigate event');
            const event = new CustomEvent('app-navigate', { 
                detail: { 
                    page: 'super-admin',
                    id: null,
                    action: null,
                    params: {}
                } 
            });
            logEvent('Dispatching event', event.detail);
            window.dispatchEvent(event);
        }
        
        function testUrlChange() {
            logEvent('Testing URL change with history.pushState');
            const url = window.getAppUrl('/super-admin');
            logEvent('Pushing state', { url });
            window.history.pushState({}, '', url);
            
            // Manually trigger popstate to simulate navigation
            setTimeout(() => {
                logEvent('Triggering popstate event');
                window.dispatchEvent(new PopStateEvent('popstate'));
            }, 100);
        }
        
        // Test authentication status
        async function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                logEvent('No token found');
                return;
            }
            
            try {
                logEvent('Checking auth status');
                const response = await fetch(window.APP_CONFIG.BASE_PATH + '/api/verify-token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                logEvent('Auth check result', {
                    success: data.success,
                    userRole: data.user ? data.user.role : 'unknown',
                    isSuperAdmin: data.user ? data.user.role === 'super_admin' : false
                });
                
            } catch (error) {
                logEvent('Auth check error', { error: error.message });
            }
        }
        
        // Check auth status on load
        setTimeout(checkAuthStatus, 1000);
    </script>
</body>
</html>
