<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Role - Bizma</title>
    <script src="config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👤 User Role Test</h1>
        <p>Test user authentication and role verification</p>
        
        <button onclick="testUserRole()">Test Current User Role</button>
        <button onclick="testNavigation()">Test Super Admin Navigation</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testUserRole() {
            const resultDiv = document.getElementById('result');
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No authentication token found. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Testing user role...</div>';
            
            try {
                const response = await fetch(window.APP_CONFIG.BASE_PATH + '/api/verify-token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const user = data.user;
                    const isSuperAdmin = user.role === 'super_admin';
                    
                    resultDiv.innerHTML = `
                        <div class="${isSuperAdmin ? 'success' : 'info'}">
                            <h3>✅ User Authentication Successful</h3>
                            <p><strong>Name:</strong> ${user.name}</p>
                            <p><strong>Email:</strong> ${user.email}</p>
                            <p><strong>Role:</strong> ${user.role}</p>
                            <p><strong>Is Super Admin:</strong> ${isSuperAdmin ? 'YES' : 'NO'}</p>
                            <p><strong>Should Access Super Admin:</strong> ${isSuperAdmin ? 'YES' : 'NO'}</p>
                        </div>
                        <div class="info">
                            <h4>Full User Data:</h4>
                            <pre>${JSON.stringify(user, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Authentication Failed</h3>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function testNavigation() {
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="info">Testing navigation to super admin dashboard...</div>';
            
            // Test different navigation methods
            console.log('Testing navigation methods:');
            
            // Method 1: Direct URL
            console.log('1. Direct URL navigation');
            const directUrl = window.getAppUrl('/super-admin');
            console.log('Direct URL:', directUrl);
            
            // Method 2: App navigate event
            console.log('2. App navigate event');
            window.dispatchEvent(new CustomEvent('app-navigate', { 
                detail: { 
                    page: 'super-admin',
                    id: null,
                    action: null,
                    params: {}
                } 
            }));
            
            // Method 3: History push state
            console.log('3. History push state');
            window.history.pushState({}, '', directUrl);
            
            resultDiv.innerHTML += `
                <div class="info">
                    <h4>Navigation Test Complete</h4>
                    <p>Check browser console for detailed logs.</p>
                    <p>Current URL: ${window.location.href}</p>
                    <p>Expected URL: ${directUrl}</p>
                </div>
            `;
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testUserRole, 1000);
        });
    </script>
</body>
</html>
