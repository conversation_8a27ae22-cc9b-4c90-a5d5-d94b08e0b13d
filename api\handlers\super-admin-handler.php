<?php
/**
 * Super Admin Handler
 * Handles super admin operations for SaaS management
 */

require_once __DIR__ . '/../db-config.php';

// Use the getCurrentUser function from db-config.php

function handleSuperAdmin($action, $subAction = null) {
    // Verify super admin access
    $user = getCurrentUser();
    if (!$user || $user['role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['error' => 'Super admin access required']);
        return;
    }

    // Log admin action
    logAdminAction($user['object_id'], $action, $subAction);

    switch ($action) {
        case 'dashboard':
            return getSuperAdminDashboard();
        case 'companies':
            return handleCompanyManagement($subAction);
        case 'business-types':
            return handleBusinessTypes($subAction);
        case 'users':
            return handleUserManagement($subAction);
        case 'subscriptions':
            return handleSubscriptionManagement($subAction);
        case 'analytics':
            return handleAnalytics($subAction);
        case 'policy-pages':
            return handlePolicyPages($subAction);
        case 'settings':
            return handleSystemSettings($subAction);
        case 'audit':
            return handleAuditLogs($subAction);
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid super admin action']);
            return;
    }
}

function getSuperAdminDashboard() {
    global $conn;
    
    try {
        // Get system statistics
        $stats = [];
        
        // Total companies
        $result = $conn->query("SELECT COUNT(*) as count FROM companies WHERE status = 'active'");
        $stats['total_companies'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // Total users
        $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
        $stats['total_users'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // Check if subscriptions table exists
        $tableExists = false;
        $checkTableSql = "SHOW TABLES LIKE 'subscriptions'";
        $result = $conn->query($checkTableSql);
        if ($result && $result->num_rows > 0) {
            // Active subscriptions
            $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'");
            $stats['active_subscriptions'] = $result ? $result->fetch_assoc()['count'] : 0;
        } else {
            $stats['active_subscriptions'] = 0;
        }
        
        // Check if payments table exists
        $tableExists = false;
        $checkTableSql = "SHOW TABLES LIKE 'payments'";
        $result = $conn->query($checkTableSql);
        if ($result && $result->num_rows > 0) {
            // Monthly revenue
            $result = $conn->query("SELECT SUM(amount) as revenue FROM payments WHERE status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
            $stats['monthly_revenue'] = $result && $result->fetch_assoc()['revenue'] ? $result->fetch_assoc()['revenue'] : 0;
        } else {
            $stats['monthly_revenue'] = 0;
        }
        
        // Recent companies (last 7 days)
        $result = $conn->query("SELECT COUNT(*) as count FROM companies WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stats['new_companies_week'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // Recent users (last 7 days)
        $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stats['new_users_week'] = $result ? $result->fetch_assoc()['count'] : 0;
        
        // Get recent activity
        $sql = "SELECT c.name as company_name, c.created_at, u.name as owner_name, u.email as owner_email
                FROM companies c 
                LEFT JOIN users u ON c.owner_id = u.object_id 
                ORDER BY c.created_at DESC LIMIT 10";
        $result = $conn->query($sql);
        $recent_companies = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $recent_companies[] = $row;
            }
        }
        
        // Check if subscriptions table exists
        $subscription_distribution = [];
        $tableExists = false;
        $checkTableSql = "SHOW TABLES LIKE 'subscriptions'";
        $result = $conn->query($checkTableSql);
        if ($result && $result->num_rows > 0) {
            // Get subscription distribution
            $sql = "SELECT plan_id, COUNT(*) as count FROM subscriptions WHERE status = 'active' GROUP BY plan_id";
            $result = $conn->query($sql);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $subscription_distribution[] = $row;
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recent_companies' => $recent_companies,
                'subscription_distribution' => $subscription_distribution
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch dashboard data: ' . $e->getMessage()]);
    }
}

function handleCompanyManagement($subAction) {
    global $conn;
    
    switch ($subAction) {
        case 'list':
            return listAllCompanies();
        case 'stats':
            return getCompanyStats();
        case 'details':
            return getCompanyDetails();
        case 'status':
            return handleCompanyStatusChange();
        case 'suspend':
            return suspendCompany();
        case 'activate':
            return activateCompany();
        case 'delete':
            return deleteCompany();
        default:
            return listAllCompanies();
    }
}

function listAllCompanies() {
    global $conn;
    
    try {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE 1=1";
        $params = [];
        $types = "";
        
        if ($search) {
            $whereClause .= " AND (c.name LIKE ? OR c.email LIKE ? OR u.name LIKE ?)";
            $searchParam = "%$search%";
            $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
            $types .= "sss";
        }
        
        if ($status) {
            $whereClause .= " AND c.status = ?";
            $params[] = $status;
            $types .= "s";
        }
        
        $sql = "SELECT c.*, u.name as owner_name, u.email as owner_email, u.last_login,
                       (SELECT COUNT(*) FROM users WHERE company_id = c.object_id) as user_count,
                       (SELECT plan_id FROM subscriptions WHERE company_id = c.object_id AND status = 'active' LIMIT 1) as current_plan
                FROM companies c 
                LEFT JOIN users u ON c.owner_id = u.object_id 
                $whereClause
                ORDER BY c.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        $types .= "ii";
        
        $stmt = $conn->prepare($sql);
        if ($types) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $companies = [];
        while ($row = $result->fetch_assoc()) {
            $companies[] = $row;
        }
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM companies c LEFT JOIN users u ON c.owner_id = u.object_id $whereClause";
        $stmt = $conn->prepare($countSql);
        if ($types && count($params) > 2) {
            $countTypes = substr($types, 0, -2); // Remove limit and offset types
            $countParams = array_slice($params, 0, -2); // Remove limit and offset params
            if ($countTypes) {
                $stmt->bind_param($countTypes, ...$countParams);
            }
        }
        $stmt->execute();
        $totalResult = $stmt->get_result();
        $total = $totalResult->fetch_assoc()['total'];
        
        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => $companies,
                'pagination' => [
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch companies: ' . $e->getMessage()]);
    }
}

function getCompanyStats() {
    global $conn;

    try {
        $stats = [];

        // Total companies
        $result = $conn->query("SELECT COUNT(*) as count FROM companies");
        $stats['total'] = $result ? $result->fetch_assoc()['count'] : 0;

        // Active companies
        $result = $conn->query("SELECT COUNT(*) as count FROM companies WHERE status = 'active'");
        $stats['active'] = $result ? $result->fetch_assoc()['count'] : 0;

        // Trial companies
        $result = $conn->query("SELECT COUNT(*) as count FROM companies c LEFT JOIN subscriptions s ON c.object_id = s.company_id WHERE s.status = 'trial' OR s.status IS NULL");
        $stats['trial'] = $result ? $result->fetch_assoc()['count'] : 0;

        // Expired companies
        $result = $conn->query("SELECT COUNT(*) as count FROM companies c LEFT JOIN subscriptions s ON c.object_id = s.company_id WHERE s.status = 'expired'");
        $stats['expired'] = $result ? $result->fetch_assoc()['count'] : 0;

        echo json_encode([
            'success' => true,
            'data' => $stats
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch company stats: ' . $e->getMessage()]);
    }
}

function handleCompanyStatusChange() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $companyId = $input['company_id'] ?? null;
        $newStatus = $input['status'] ?? null;

        if (!$companyId || !$newStatus) {
            http_response_code(400);
            echo json_encode(['error' => 'Company ID and status are required']);
            return;
        }

        $sql = "UPDATE companies SET status = ?, updated_at = NOW() WHERE object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $newStatus, $companyId);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Company status updated successfully'
            ]);
        } else {
            throw new Exception('Failed to update company status');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update company status: ' . $e->getMessage()]);
    }
}

function getCompanyDetails() {
    global $conn;

    try {
        $companyId = $_GET['company_id'] ?? null;

        if (!$companyId) {
            http_response_code(400);
            echo json_encode(['error' => 'Company ID is required']);
            return;
        }

        // Get company details with owner info
        $sql = "SELECT c.*, u.name as owner_name, u.email as owner_email, u.last_login,
                       (SELECT COUNT(*) FROM users WHERE company_id = c.object_id) as user_count
                FROM companies c
                LEFT JOIN users u ON c.owner_id = u.object_id
                WHERE c.object_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $companyId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($company = $result->fetch_assoc()) {
            // Get subscription info
            $subSql = "SELECT * FROM subscriptions WHERE company_id = ? ORDER BY created_at DESC LIMIT 1";
            $subStmt = $conn->prepare($subSql);
            $subStmt->bind_param("s", $companyId);
            $subStmt->execute();
            $subResult = $subStmt->get_result();
            $subscription = $subResult->fetch_assoc();

            // Get users
            $usersSql = "SELECT object_id, name, email, role, status, last_login FROM users WHERE company_id = ?";
            $usersStmt = $conn->prepare($usersSql);
            $usersStmt->bind_param("s", $companyId);
            $usersStmt->execute();
            $usersResult = $usersStmt->get_result();

            $users = [];
            while ($user = $usersResult->fetch_assoc()) {
                $users[] = $user;
            }

            echo json_encode([
                'success' => true,
                'data' => [
                    'company' => $company,
                    'subscription' => $subscription,
                    'users' => $users
                ]
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Company not found']);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch company details: ' . $e->getMessage()]);
    }
}

function handleBusinessTypes($subAction) {
    global $conn;

    switch ($subAction) {
        case 'templates':
            return handleBusinessTypeTemplates();
        default:
            return getBusinessTypes();
    }
}

function getBusinessTypes() {
    global $conn;

    try {
        $sql = "SELECT * FROM business_types ORDER BY name ASC";
        $result = $conn->query($sql);

        $businessTypes = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $businessTypes[] = $row;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $businessTypes
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch business types: ' . $e->getMessage()]);
    }
}

function handleBusinessTypeTemplates() {
    global $conn;

    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            return getBusinessTypeTemplates();
        case 'POST':
            return createBusinessTypeTemplate();
        case 'PUT':
            return updateBusinessTypeTemplate();
        case 'DELETE':
            return deleteBusinessTypeTemplate();
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function getBusinessTypeTemplates() {
    global $conn;

    try {
        $businessTypeId = $_GET['business_type_id'] ?? null;

        if ($businessTypeId) {
            $sql = "SELECT * FROM business_type_templates WHERE business_type_id = ? ORDER BY name ASC";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $businessTypeId);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $sql = "SELECT btt.*, bt.name as business_type_name
                    FROM business_type_templates btt
                    LEFT JOIN business_types bt ON btt.business_type_id = bt.id
                    ORDER BY bt.name ASC, btt.name ASC";
            $result = $conn->query($sql);
        }

        $templates = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                if (isset($row['template_data'])) {
                    $row['template_data'] = json_decode($row['template_data'], true);
                }
                $templates[] = $row;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $templates
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch templates: ' . $e->getMessage()]);
    }
}

function createBusinessTypeTemplate() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $businessTypeId = $input['business_type_id'] ?? null;
        $name = $input['name'] ?? null;
        $description = $input['description'] ?? '';
        $templateData = $input['template_data'] ?? [];

        if (!$businessTypeId || !$name) {
            http_response_code(400);
            echo json_encode(['error' => 'Business type ID and name are required']);
            return;
        }

        $sql = "INSERT INTO business_type_templates (business_type_id, name, description, template_data, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $templateDataJson = json_encode($templateData);
        $stmt->bind_param("isss", $businessTypeId, $name, $description, $templateDataJson);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Template created successfully',
                'id' => $conn->insert_id
            ]);
        } else {
            throw new Exception('Failed to create template');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create template: ' . $e->getMessage()]);
    }
}

function updateBusinessTypeTemplate() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $id = $input['id'] ?? null;
        $name = $input['name'] ?? null;
        $description = $input['description'] ?? '';
        $templateData = $input['template_data'] ?? [];

        if (!$id || !$name) {
            http_response_code(400);
            echo json_encode(['error' => 'Template ID and name are required']);
            return;
        }

        $sql = "UPDATE business_type_templates
                SET name = ?, description = ?, template_data = ?, updated_at = NOW()
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $templateDataJson = json_encode($templateData);
        $stmt->bind_param("sssi", $name, $description, $templateDataJson, $id);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Template updated successfully'
            ]);
        } else {
            throw new Exception('Failed to update template');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update template: ' . $e->getMessage()]);
    }
}

function deleteBusinessTypeTemplate() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $id = $input['id'] ?? null;

        if (!$id) {
            http_response_code(400);
            echo json_encode(['error' => 'Template ID is required']);
            return;
        }

        $sql = "DELETE FROM business_type_templates WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Template deleted successfully'
            ]);
        } else {
            throw new Exception('Failed to delete template');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete template: ' . $e->getMessage()]);
    }
}

function handleUserManagement($subAction) {
    global $conn;
    
    switch ($subAction) {
        case 'list':
            return listAllUsers();
        case 'details':
            return getUserDetails();
        case 'suspend':
            return suspendUser();
        case 'activate':
            return activateUser();
        case 'change-role':
            return changeUserRole();
        default:
            return listAllUsers();
    }
}

function listAllUsers() {
    global $conn;
    
    try {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $search = $_GET['search'] ?? '';
        $role = $_GET['role'] ?? '';
        $status = $_GET['status'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        $whereClause = "WHERE u.role != 'super_admin'"; // Don't show super admins
        $params = [];
        $types = "";
        
        if ($search) {
            $whereClause .= " AND (u.name LIKE ? OR u.email LIKE ? OR c.name LIKE ?)";
            $searchParam = "%$search%";
            $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
            $types .= "sss";
        }
        
        if ($role) {
            $whereClause .= " AND u.role = ?";
            $params[] = $role;
            $types .= "s";
        }
        
        if ($status) {
            $whereClause .= " AND u.status = ?";
            $params[] = $status;
            $types .= "s";
        }
        
        $sql = "SELECT u.*, c.name as company_name
                FROM users u 
                LEFT JOIN companies c ON u.company_id = c.object_id 
                $whereClause
                ORDER BY u.created_at DESC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        $types .= "ii";
        
        $stmt = $conn->prepare($sql);
        if ($types) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $users = [];
        while ($row = $result->fetch_assoc()) {
            // Remove sensitive data
            unset($row['password_hash'], $row['auth_token']);
            $users[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'users' => $users,
                'pagination' => [
                    'page' => (int)$page,
                    'limit' => (int)$limit
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch users: ' . $e->getMessage()]);
    }
}

function logAdminAction($adminUserId, $action, $details = null, $targetType = null, $targetId = null) {
    global $conn;
    
    try {
        // Check if admin_audit_log table exists
        $tableExists = false;
        $checkTableSql = "SHOW TABLES LIKE 'admin_audit_log'";
        $result = $conn->query($checkTableSql);
        if ($result && $result->num_rows > 0) {
            $tableExists = true;
        }
        
        // Only log if table exists
        if ($tableExists) {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $detailsJson = $details ? json_encode($details) : null;
            
            $sql = "INSERT INTO admin_audit_log (admin_user_id, action, target_type, target_id, details, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("sssssss", $adminUserId, $action, $targetType, $targetId, $detailsJson, $ipAddress, $userAgent);
            $stmt->execute();
        }
    } catch (Exception $e) {
        // Log error but don't fail the main operation
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}

function handleAnalytics($subAction) {
    global $conn;
    
    switch ($subAction) {
        case 'revenue':
            return getRevenueAnalytics();
        case 'growth':
            return getGrowthAnalytics();
        case 'usage':
            return getUsageAnalytics();
        default:
            return getAnalytics();
    }
}

function getRevenueAnalytics() {
    global $conn;
    
    try {
        // Monthly revenue for last 12 months
        $sql = "SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    SUM(amount) as revenue,
                    COUNT(*) as transactions
                FROM payments 
                WHERE status = 'completed' 
                    AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month";
        
        $result = $conn->query($sql);
        $monthlyRevenue = [];
        while ($row = $result->fetch_assoc()) {
            $monthlyRevenue[] = $row;
        }
        
        // Revenue by plan
        $sql = "SELECT
                    s.plan_id,
                    SUM(p.amount) as revenue,
                    COUNT(p.id) as transactions
                FROM payments p
                JOIN subscriptions s ON p.reference_id = s.object_id
                WHERE p.status = 'completed'
                    AND p.type = 'subscription'
                    AND p.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY s.plan_id";
        
        $result = $conn->query($sql);
        $revenueByPlan = [];
        while ($row = $result->fetch_assoc()) {
            $revenueByPlan[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'data' => [
                'monthly_revenue' => $monthlyRevenue,
                'revenue_by_plan' => $revenueByPlan
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch revenue analytics: ' . $e->getMessage()]);
    }
}

function handleSubscriptionManagement($subAction) {
    error_log("handleSubscriptionManagement called with subAction: " . $subAction);

    switch ($subAction) {
        case 'list':
            return getSubscriptionsList();
        default:
            error_log("Invalid subscription action: " . $subAction);
            http_response_code(400);
            echo json_encode(['error' => 'Invalid subscription action: ' . $subAction]);
            return;
    }
}

function getSubscriptionsList() {
    global $conn;

    try {
        // Check if subscriptions table exists
        $checkTableSql = "SHOW TABLES LIKE 'subscriptions'";
        $result = $conn->query($checkTableSql);
        if (!$result || $result->num_rows === 0) {
            // Table doesn't exist, return empty data
            echo json_encode([
                'success' => true,
                'data' => [
                    'subscriptions' => [],
                    'pagination' => [
                        'page' => 1,
                        'limit' => 20,
                        'total' => 0,
                        'pages' => 0
                    ]
                ],
                'message' => 'Subscriptions table not found - no subscription data available'
            ]);
            return;
        }

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        $offset = ($page - 1) * $limit;

        $sql = "SELECT s.*, c.name as company_name, c.email as company_email
                FROM subscriptions s
                LEFT JOIN companies c ON s.company_id = c.object_id
                ORDER BY s.created_at DESC
                LIMIT ? OFFSET ?";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $limit, $offset);
        $stmt->execute();
        $result = $stmt->get_result();

        $subscriptions = [];
        while ($row = $result->fetch_assoc()) {
            $subscriptions[] = $row;
        }

        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM subscriptions";
        $countResult = $conn->query($countSql);
        $total = $countResult->fetch_assoc()['total'];

        echo json_encode([
            'success' => true,
            'data' => [
                'subscriptions' => $subscriptions,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch subscriptions: ' . $e->getMessage()]);
    }
}

function getAnalytics() {
    global $conn;

    try {
        $days = isset($_GET['days']) ? (int)$_GET['days'] : 30;

        // Revenue analytics
        $revenueSql = "SELECT SUM(amount) as total FROM payments WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($revenueSql);
        $stmt->bind_param("i", $days);
        $stmt->execute();
        $revenue = $stmt->get_result()->fetch_assoc();

        // Growth rate (compare with previous period)
        $prevRevenueSql = "SELECT SUM(amount) as total FROM payments WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($prevRevenueSql);
        $doubleDays = $days * 2;
        $stmt->bind_param("ii", $doubleDays, $days);
        $stmt->execute();
        $prevRevenue = $stmt->get_result()->fetch_assoc();

        $growthRate = 0;
        if ($prevRevenue['total'] > 0) {
            $growthRate = (($revenue['total'] - $prevRevenue['total']) / $prevRevenue['total']) * 100;
        }

        // New customers
        $newCustomersSql = "SELECT COUNT(*) as count FROM companies WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($newCustomersSql);
        $stmt->bind_param("i", $days);
        $stmt->execute();
        $newCustomers = $stmt->get_result()->fetch_assoc();

        // Churn rate (cancelled subscriptions)
        $churnSql = "SELECT COUNT(*) as cancelled FROM subscriptions WHERE status = 'cancelled' AND updated_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $conn->prepare($churnSql);
        $stmt->bind_param("i", $days);
        $stmt->execute();
        $churnData = $stmt->get_result()->fetch_assoc();

        $totalSql = "SELECT COUNT(*) as total FROM subscriptions WHERE status = 'active'";
        $totalSubs = $conn->query($totalSql)->fetch_assoc();

        $churnRate = $totalSubs['total'] > 0 ? ($churnData['cancelled'] / $totalSubs['total']) * 100 : 0;

        // Top performing plans
        $plansSql = "SELECT plan_id as name, COUNT(*) as count FROM subscriptions WHERE status = 'active' GROUP BY plan_id ORDER BY count DESC LIMIT 5";
        $plansResult = $conn->query($plansSql);
        $topPlans = [];
        while ($row = $plansResult->fetch_assoc()) {
            $topPlans[] = $row;
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'revenue' => ['total' => $revenue['total'] ?: 0],
                'growth' => ['rate' => round($growthRate, 2)],
                'customers' => ['new' => $newCustomers['count']],
                'churn' => ['rate' => round($churnRate, 2)],
                'top_plans' => $topPlans,
                'recent_activity' => []
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch analytics: ' . $e->getMessage()]);
    }
}

function handleSystemSettings($subAction = null) {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        return getSettings();
    } else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        return updateSettings();
    }
}

function getSettings() {
    global $conn;

    try {
        $sql = "SELECT setting_key, setting_value FROM system_settings";
        $result = $conn->query($sql);

        $settings = [];
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = json_decode($row['setting_value'], true) ?: $row['setting_value'];
        }

        echo json_encode([
            'success' => true,
            'data' => ['settings' => $settings]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch settings: ' . $e->getMessage()]);
    }
}

function updateSettings() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $settings = $input['settings'] ?? [];

        foreach ($settings as $key => $value) {
            $sql = "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            $stmt = $conn->prepare($sql);
            $jsonValue = is_array($value) || is_object($value) ? json_encode($value) : $value;
            $stmt->bind_param("ss", $key, $jsonValue);
            $stmt->execute();
        }

        echo json_encode([
            'success' => true,
            'message' => 'Settings updated successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update settings: ' . $e->getMessage()]);
    }
}

function handlePolicyPages($subAction) {
    global $conn;

    try {
        switch ($subAction) {
            case 'list':
                return getPolicyPagesList();
            case 'update':
                return updatePolicyPages();
            default:
                return getPolicyPagesList();
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to handle policy pages: ' . $e->getMessage()]);
    }
}

function getPolicyPagesList() {
    global $conn;

    try {
        // Get all policy pages settings
        $sql = "SELECT * FROM settings WHERE setting_key LIKE 'policy_%' OR setting_key LIKE 'terms_%' OR setting_key LIKE 'privacy_%'";
        $result = $conn->query($sql);

        $policyPages = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $policyPages[$row['setting_key']] = $row['setting_value'];
            }
        }

        // If no policy pages exist, create default structure
        if (empty($policyPages)) {
            $defaultPages = [
                'privacy_policy' => 'Privacy Policy content goes here...',
                'terms_of_service' => 'Terms of Service content goes here...',
                'refund_policy' => 'Refund Policy content goes here...',
                'cookie_policy' => 'Cookie Policy content goes here...'
            ];

            foreach ($defaultPages as $key => $value) {
                $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value, created_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
                $stmt->bind_param("ss", $key, $value);
                $stmt->execute();
                $policyPages[$key] = $value;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $policyPages
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch policy pages: ' . $e->getMessage()]);
    }
}

function updatePolicyPages() {
    global $conn;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !is_array($input)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input data']);
            return;
        }

        $allowedKeys = ['privacy_policy', 'terms_of_service', 'refund_policy', 'cookie_policy'];
        $updated = 0;

        foreach ($input as $key => $value) {
            if (in_array($key, $allowedKeys)) {
                $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value, created_at) VALUES (?, ?, NOW()) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()");
                $stmt->bind_param("ss", $key, $value);

                if ($stmt->execute()) {
                    $updated++;
                }
            }
        }

        echo json_encode([
            'success' => true,
            'message' => "Updated $updated policy pages successfully"
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update policy pages: ' . $e->getMessage()]);
    }
}

?>
