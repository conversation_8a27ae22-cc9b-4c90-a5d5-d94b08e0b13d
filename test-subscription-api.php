<?php
/**
 * Test script to debug subscription API issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/api/db-config.php';

try {
    echo "Testing Subscription Management API...\n\n";
    
    // 1. Check database connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    echo "✅ Database connection successful\n";
    
    // 2. Check if users table exists and has data
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows > 0) {
        echo "✅ Users table exists\n";
        
        // Check if there are any users
        $result = $conn->query("SELECT COUNT(*) as count FROM users");
        $row = $result->fetch_assoc();
        echo "Total users in database: " . $row['count'] . "\n";
        
        if ($row['count'] > 0) {
            // Get a sample user
            $result = $conn->query("SELECT object_id, email, role FROM users LIMIT 1");
            $user = $result->fetch_assoc();
            echo "Sample user: " . $user['email'] . " (Role: " . $user['role'] . ")\n";
        }
    } else {
        echo "❌ Users table does not exist\n";
    }
    
    // 3. Check if subscriptions table exists
    $result = $conn->query("SHOW TABLES LIKE 'subscriptions'");
    if ($result->num_rows > 0) {
        echo "✅ Subscriptions table exists\n";
        
        // Check table structure
        $result = $conn->query("DESCRIBE subscriptions");
        echo "\nSubscriptions table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
        }
        
        // Check if there are any subscriptions
        $result = $conn->query("SELECT COUNT(*) as count FROM subscriptions");
        $row = $result->fetch_assoc();
        echo "\nTotal subscriptions in database: " . $row['count'] . "\n";
        
    } else {
        echo "❌ Subscriptions table does not exist\n";
        echo "Creating subscriptions table...\n";
        
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS subscriptions (
            object_id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) NOT NULL,
            company_id VARCHAR(36),
            plan_id VARCHAR(50) NOT NULL,
            status VARCHAR(20) DEFAULT 'active',
            billing_cycle VARCHAR(20) DEFAULT 'monthly',
            amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            currency VARCHAR(3) DEFAULT 'INR',
            trial_start_date DATETIME,
            trial_end_date DATETIME,
            subscription_start_date DATETIME,
            subscription_end_date DATETIME,
            next_billing_date DATETIME,
            payment_method VARCHAR(50),
            payment_gateway VARCHAR(50),
            gateway_subscription_id VARCHAR(255),
            metadata JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_company_id (company_id),
            INDEX idx_plan_id (plan_id),
            INDEX idx_status (status),
            INDEX idx_trial_end_date (trial_end_date),
            INDEX idx_next_billing_date (next_billing_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createTableSQL)) {
            echo "✅ Subscriptions table created successfully\n";
        } else {
            throw new Exception("Failed to create subscriptions table: " . $conn->error);
        }
    }
    
    // 4. Test subscription API routing
    echo "\n--- Testing Subscription API Routing ---\n";
    
    // Test URL parsing
    $testUrls = [
        '/biz/api/api.php/subscription-management/current',
        '/biz/api/api.php/subscription-management/trial-status',
        '/biz/api/subscription-management.php/current'
    ];
    
    foreach ($testUrls as $url) {
        echo "\nTesting URL: " . $url . "\n";
        
        $pathParts = explode('/', trim($url, '/'));
        echo "Path parts: " . implode(', ', $pathParts) . "\n";
        
        $apiIndex = array_search('api.php', $pathParts);
        if ($apiIndex !== false) {
            $action = isset($pathParts[$apiIndex + 3]) ? $pathParts[$apiIndex + 3] : 'current';
            echo "Action (via api.php): " . $action . "\n";
        } else {
            $action = $pathParts[3] ?? 'current';
            echo "Action (direct): " . $action . "\n";
        }
    }
    
    // 5. Test getCurrentUser function
    echo "\n--- Testing getCurrentUser Function ---\n";
    
    // Mock authorization header
    $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer test-token-123';
    
    try {
        $user = getCurrentUser();
        if ($user) {
            echo "✅ getCurrentUser returned user: " . $user['email'] . "\n";
        } else {
            echo "❌ getCurrentUser returned null (no valid token)\n";
        }
    } catch (Exception $e) {
        echo "❌ Error in getCurrentUser: " . $e->getMessage() . "\n";
    }
    
    // 6. Test subscription functions
    echo "\n--- Testing Subscription Functions ---\n";
    
    // Check if subscription management functions exist
    $functions = [
        'getCurrentSubscription',
        'getTrialStatus',
        'getUsageStats'
    ];
    
    foreach ($functions as $function) {
        if (function_exists($function)) {
            echo "✅ Function exists: " . $function . "\n";
        } else {
            echo "❌ Function missing: " . $function . "\n";
        }
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
