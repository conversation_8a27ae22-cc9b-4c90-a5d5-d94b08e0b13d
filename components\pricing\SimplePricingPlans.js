// Simple Pricing Plans Component
function SimplePricingPlans({ selectedPlan, onPlanSelect, showTitle = true }) {
    const [billingCycle, setBillingCycle] = React.useState('monthly');
    
    const features = [
        'Customer Management',
        'Invoice Generation', 
        'Quotation Management',
        'Contract Management',
        'Lead Tracking',
        'Business Analytics',
        'Email Notifications',
        'Data Export',
        'Multi-user Access',
        '24/7 Support'
    ];

    const planDetails = {
        monthly: {
            price: 500,
            period: 'month',
            savings: null
        },
        yearly: {
            price: 5000,
            period: 'year',
            savings: 1000 // 12 * 500 - 5000 = 1000
        }
    };

    const handlePlanSelect = (cycle) => {
        setBillingCycle(cycle);
        if (onPlanSelect) {
            onPlanSelect({
                plan: 'basic',
                cycle: cycle,
                price: planDetails[cycle].price
            });
        }
    };

    return (
        <div className="max-w-4xl mx-auto px-4 py-8">
            {showTitle && (
                <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                        Simple, Transparent Pricing
                    </h2>
                    <p className="text-lg text-gray-600">
                        One plan with everything you need to manage your business
                    </p>
                </div>
            )}

            {/* Billing Toggle */}
            <div className="flex justify-center mb-8">
                <div className="bg-gray-100 p-1 rounded-lg flex">
                    <button
                        onClick={() => handlePlanSelect('monthly')}
                        className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                            billingCycle === 'monthly'
                                ? 'bg-white text-blue-600 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        Monthly
                    </button>
                    <button
                        onClick={() => handlePlanSelect('yearly')}
                        className={`px-6 py-2 rounded-md text-sm font-medium transition-all relative ${
                            billingCycle === 'yearly'
                                ? 'bg-white text-blue-600 shadow-sm'
                                : 'text-gray-600 hover:text-gray-900'
                        }`}
                    >
                        Yearly
                        <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                            Save ₹1,000
                        </span>
                    </button>
                </div>
            </div>

            {/* Pricing Card */}
            <div className="max-w-md mx-auto">
                <div className="bg-white rounded-2xl shadow-xl border-2 border-blue-500 relative overflow-hidden">
                    {/* Popular Badge */}
                    <div className="bg-blue-500 text-white text-center py-2 px-4">
                        <span className="text-sm font-semibold">Most Popular Plan</span>
                    </div>

                    <div className="p-8">
                        {/* Plan Name */}
                        <div className="text-center mb-6">
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">
                                Business Plan
                            </h3>
                            <p className="text-gray-600">
                                Complete business management solution
                            </p>
                        </div>

                        {/* Price */}
                        <div className="text-center mb-8">
                            <div className="flex items-baseline justify-center">
                                <span className="text-4xl font-bold text-gray-900">
                                    ₹{planDetails[billingCycle].price.toLocaleString()}
                                </span>
                                <span className="text-lg text-gray-600 ml-2">
                                    /{planDetails[billingCycle].period}
                                </span>
                            </div>
                            {billingCycle === 'yearly' && (
                                <div className="mt-2">
                                    <span className="text-sm text-green-600 font-medium">
                                        Save ₹1,000 compared to monthly billing
                                    </span>
                                </div>
                            )}
                            {billingCycle === 'monthly' && (
                                <div className="mt-2">
                                    <span className="text-sm text-gray-500">
                                        ₹{(planDetails[billingCycle].price / 30).toFixed(0)}/day
                                    </span>
                                </div>
                            )}
                        </div>

                        {/* Features */}
                        <div className="mb-8">
                            <h4 className="text-lg font-semibold text-gray-900 mb-4">
                                Everything included:
                            </h4>
                            <ul className="space-y-3">
                                {features.map((feature, index) => (
                                    <li key={index} className="flex items-center">
                                        <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-gray-700">{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* CTA Button */}
                        <button
                            onClick={() => handlePlanSelect(billingCycle)}
                            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                        >
                            Get Started Now
                        </button>

                        {/* Additional Info */}
                        <div className="mt-4 text-center">
                            <p className="text-sm text-gray-500">
                                No setup fees • Cancel anytime • 24/7 support
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-12 text-center">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
                    <div className="flex items-center justify-center space-x-2">
                        <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600">Secure & Reliable</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                        <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                        <span className="text-sm text-gray-600">24/7 Support</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                        <svg className="w-5 h-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600">Easy Setup</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Make component globally available
window.SimplePricingPlans = SimplePricingPlans;
