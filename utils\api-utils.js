/**
 * Enhanced API Utilities
 * Provides robust API communication with error handling, retry logic, and caching
 */

// API Configuration
const API_BASE_URL = window.APP_CONFIG ? window.APP_CONFIG.API_BASE_URL : '/api/api.php';
const DEFAULT_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Request cache for GET requests
const requestCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Get authentication token
function getAuthToken() {
    return localStorage.getItem('authToken');
}

// Check if user is authenticated
function isAuthenticated() {
    return !!getAuthToken();
}

// Sleep utility for retry delays
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Generate cache key
function getCacheKey(url, options) {
    return `${url}_${JSON.stringify(options)}`;
}

// Check cache validity
function isCacheValid(cacheEntry) {
    return cacheEntry && (Date.now() - cacheEntry.timestamp) < CACHE_DURATION;
}

// Enhanced API request with retry logic and caching
async function makeApiRequest(url, options = {}) {
    const {
        method = 'GET',
        timeout = DEFAULT_TIMEOUT,
        retries = MAX_RETRIES,
        cache = method === 'GET',
        ...restOptions
    } = options;

    // Check cache for GET requests
    if (cache && method === 'GET') {
        const cacheKey = getCacheKey(url, restOptions);
        const cached = requestCache.get(cacheKey);
        if (isCacheValid(cached)) {
            console.log('🎯 Cache hit for:', url);
            return cached.data;
        }
    }

    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json',
        ...restOptions.headers
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    let lastError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            console.log(`🚀 API Request [${attempt + 1}/${retries + 1}]:`, method, url);
            
            const response = await fetch(url, {
                method,
                ...restOptions,
                headers,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            // Handle different response types
            if (!response.ok) {
                await handleApiError(response, attempt, retries);
                continue; // Retry on certain errors
            }

            const result = await parseResponse(response);
            
            // Cache successful GET requests
            if (cache && method === 'GET') {
                const cacheKey = getCacheKey(url, restOptions);
                requestCache.set(cacheKey, {
                    data: result,
                    timestamp: Date.now()
                });
            }

            console.log('✅ API Success:', method, url);
            return result;

        } catch (error) {
            clearTimeout(timeoutId);
            lastError = error;
            
            console.warn(`⚠️ API Attempt ${attempt + 1} failed:`, error.message);
            
            // Don't retry on certain errors
            if (error.name === 'AbortError' || 
                error.message.includes('401') || 
                error.message.includes('403') ||
                attempt === retries) {
                break;
            }
            
            // Wait before retry
            if (attempt < retries) {
                await sleep(RETRY_DELAY * (attempt + 1));
            }
        }
    }

    // All retries failed
    console.error('❌ API Request failed after all retries:', url, lastError);
    
    // Handle authentication errors
    if (lastError && (lastError.message.includes('401') || lastError.message.includes('403'))) {
        handleAuthenticationError();
    }
    
    throw lastError || new Error('API request failed after all retries');
}

// Handle API errors
async function handleApiError(response, attempt, maxRetries) {
    const errorText = await response.text();
    let errorData;
    
    try {
        errorData = JSON.parse(errorText);
    } catch (e) {
        errorData = { error: errorText };
    }

    const error = new Error(`API request failed: ${response.status} ${response.statusText}`);
    error.status = response.status;
    error.data = errorData;
    error.response = response;

    // Handle specific status codes
    switch (response.status) {
        case 401:
            error.message = 'Authentication required';
            handleAuthenticationError();
            break;
        case 403:
            error.message = 'Access denied';
            break;
        case 404:
            error.message = 'Resource not found';
            break;
        case 422:
            error.message = 'Validation error';
            error.validationErrors = errorData.errors || errorData;
            break;
        case 429:
            error.message = 'Rate limit exceeded';
            // Wait for retry-after header
            const retryAfter = response.headers.get('retry-after');
            if (retryAfter && attempt < maxRetries) {
                await sleep(parseInt(retryAfter) * 1000);
                return; // Allow retry
            }
            break;
        case 500:
        case 502:
        case 503:
        case 504:
            error.message = 'Server error';
            if (attempt < maxRetries) {
                return; // Allow retry for server errors
            }
            break;
        default:
            error.message = `Request failed with status ${response.status}`;
    }

    throw error;
}

// Parse response based on content type
async function parseResponse(response) {
    const contentType = response.headers.get('content-type');
    
    if (!contentType) {
        const text = await response.text();
        return text ? { message: text } : { success: true };
    }
    
    if (contentType.includes('application/json')) {
        const text = await response.text();
        if (!text.trim()) {
            return { success: true };
        }
        
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('JSON Parse Error:', e, '\nResponse text:', text);
            throw new Error(`Invalid JSON response: ${e.message}`);
        }
    }
    
    if (contentType.includes('text/')) {
        return { message: await response.text() };
    }
    
    // For other content types, return as blob
    return { data: await response.blob() };
}

// Handle authentication errors
function handleAuthenticationError() {
    console.warn('🔐 Authentication error - clearing token and redirecting');
    localStorage.removeItem('authToken');
    
    // Trigger app-level authentication state update
    if (window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('auth-error'));
    }
    
    // Redirect to login if not already there
    if (!window.location.pathname.includes('login') && !window.location.pathname.includes('landing')) {
        const loginUrl = window.getAppUrl ? window.getAppUrl('/login') : '/login';
        window.history.pushState({}, '', loginUrl);
        if (window.location.reload) {
            window.location.reload();
        }
    }
}

// Clear cache
function clearApiCache() {
    requestCache.clear();
    console.log('🗑️ API cache cleared');
}

// Real implementation of trickleCreateObject
async function trickleCreateObject(objectType, objectData) {
    try {
        // Handle authentication requests
        if (objectType === 'auth') {
            const authType = objectData.type || 'login';
            const url = `${API_BASE_URL}/auth/${authType}`;
            
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(objectData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Auth response error:', errorText);
                throw new Error(`Authentication failed: ${response.status} - ${errorText}`);
            }

            const responseText = await response.text();
            console.log('Auth response text:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('Failed to parse auth response as JSON:', parseError);
                console.error('Response text:', responseText);
                throw new Error('Server returned invalid JSON response');
            }
            
            // Return in expected format
            return {
                objectId: data.user && data.user.id ? data.user.id : 'auth_' + Date.now(),
                objectType: 'auth',
                objectData: data,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
        }
        
        // Handle regular object creation
        const url = `${API_BASE_URL}/${objectType}`;
        console.log('Creating object:', objectType, 'at URL:', url);
        console.log('Object data:', objectData);

        const data = await makeApiRequest(url, {
            method: 'POST',
            body: JSON.stringify(objectData)
        });

        console.log('Create response:', data);
        return data;
    } catch (error) {
        console.error('Error in trickleCreateObject:', error);
        throw error;
    }
}

// Real implementation of trickleUpdateObject
async function trickleUpdateObject(objectType, objectId, objectData) {
    try {
        const url = `${API_BASE_URL}/${objectType}/${objectId}`;
        const data = await makeApiRequest(url, {
            method: 'PUT',
            body: JSON.stringify(objectData)
        });
        
        return data;
    } catch (error) {
        console.error('Error in trickleUpdateObject:', error);
        throw error;
    }
}

// Real implementation of trickleGetObject
async function trickleGetObject(objectType, objectId) {
    try {
        const url = `${API_BASE_URL}/${objectType}/${objectId}`;
        const data = await makeApiRequest(url);
        
        return data;
    } catch (error) {
        console.error('Error in trickleGetObject:', error);
        throw error;
    }
}

// Real implementation of trickleListObjects
async function trickleListObjects(objectType, limit = 100, descent = true, nextPageToken) {
    try {
        let url = `${API_BASE_URL}/${objectType}`;
        const params = new URLSearchParams();
        
        if (limit !== 100) params.append('limit', limit);
        if (!descent) params.append('descent', 'false');
        if (nextPageToken) params.append('nextPageToken', nextPageToken);
        
        if (params.toString()) {
            url += '?' + params.toString();
        }
        
        const data = await makeApiRequest(url);
        
        // Ensure consistent response format
        if (Array.isArray(data)) {
            return {
                items: data,
                nextPageToken: null
            };
        }
        
        return data;
    } catch (error) {
        console.error('Error in trickleListObjects:', error);
        throw error;
    }
}

// Real implementation of trickleDeleteObject
async function trickleDeleteObject(objectType, objectId) {
    try {
        const url = `${API_BASE_URL}/${objectType}/${objectId}`;
        await makeApiRequest(url, {
            method: 'DELETE'
        });
        
        return { success: true };
    } catch (error) {
        console.error('Error in trickleDeleteObject:', error);
        throw error;
    }
}

// Additional utility functions
async function uploadFile(file, objectType, objectId) {
    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('objectType', objectType);
        formData.append('objectId', objectId);

        const url = `${API_BASE_URL}/upload`;
        const token = getAuthToken();
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
                // Don't set Content-Type for FormData
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`Upload failed: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error uploading file:', error);
        throw error;
    }
}

// Batch operations
async function batchCreateObjects(objectType, objectsData) {
    try {
        const url = `${API_BASE_URL}/${objectType}/batch`;
        return await makeApiRequest(url, {
            method: 'POST',
            body: JSON.stringify({ objects: objectsData })
        });
    } catch (error) {
        console.error('Error in batch create:', error);
        throw error;
    }
}

async function batchUpdateObjects(objectType, updates) {
    try {
        const url = `${API_BASE_URL}/${objectType}/batch`;
        return await makeApiRequest(url, {
            method: 'PUT',
            body: JSON.stringify({ updates })
        });
    } catch (error) {
        console.error('Error in batch update:', error);
        throw error;
    }
}

async function batchDeleteObjects(objectType, objectIds) {
    try {
        const url = `${API_BASE_URL}/${objectType}/batch`;
        return await makeApiRequest(url, {
            method: 'DELETE',
            body: JSON.stringify({ ids: objectIds })
        });
    } catch (error) {
        console.error('Error in batch delete:', error);
        throw error;
    }
}

// Search functionality
async function searchObjects(objectType, query, filters = {}) {
    try {
        const url = `${API_BASE_URL}/${objectType}/search`;
        return await makeApiRequest(url, {
            method: 'POST',
            body: JSON.stringify({ query, filters })
        });
    } catch (error) {
        console.error('Error in search:', error);
        throw error;
    }
}

// Health check
async function checkApiHealth() {
    try {
        const url = `${API_BASE_URL}/health`;
        return await makeApiRequest(url, { timeout: 5000 });
    } catch (error) {
        console.error('API health check failed:', error);
        return { status: 'error', error: error.message };
    }
}

// Export functions to global scope to replace mock API
window.trickleCreateObject = trickleCreateObject;
window.trickleUpdateObject = trickleUpdateObject;
window.trickleGetObject = trickleGetObject;
window.trickleListObjects = trickleListObjects;
window.trickleDeleteObject = trickleDeleteObject;
window.makeApiRequest = makeApiRequest;
window.uploadFile = uploadFile;
window.batchCreateObjects = batchCreateObjects;
window.batchUpdateObjects = batchUpdateObjects;
window.batchDeleteObjects = batchDeleteObjects;
window.searchObjects = searchObjects;
window.checkApiHealth = checkApiHealth;
window.clearApiCache = clearApiCache;
window.isAuthenticated = isAuthenticated;

console.log('Enhanced API utilities loaded - replacing mock API functions');
