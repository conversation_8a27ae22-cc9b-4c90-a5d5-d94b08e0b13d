function LeadTasks({ leadId }) {
    try {
        const [tasks, setTasks] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [newTask, setNewTask] = React.useState({
            title: '',
            description: '',
            dueDate: '',
            priority: 'medium',
            status: 'pending'
        });

        React.useEffect(() => {
            fetchTasks();
        }, [leadId]);

        const fetchTasks = async () => {
            try {
                setLoading(true);
                const response = await trickleListObjects(`task:${leadId}`, 50, true);
                setTasks(response.items);
            } catch (error) {
                console.error('Error fetching tasks:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleAddTask = async (e) => {
            e.preventDefault();
            if (!newTask.title.trim()) return;

            try {
                await trickleCreateObject(`task:${leadId}`, {
                    ...newTask,
                    createdAt: new Date().toISOString()
                });

                // Also create an activity for the task
                await trickleCreateObject(`activity:${leadId}`, {
                    type: 'task',
                    description: `New task created: ${newTask.title}`,
                    createdAt: new Date().toISOString()
                });

                setNewTask({
                    title: '',
                    description: '',
                    dueDate: '',
                    priority: 'medium',
                    status: 'pending'
                });
                fetchTasks();
            } catch (error) {
                console.error('Error adding task:', error);
            }
        };

        const handleTaskUpdate = async (taskId, updatedData) => {
            try {
                await trickleUpdateObject(`task:${leadId}`, taskId, {
                    ...updatedData,
                    updatedAt: new Date().toISOString()
                });

                // Create activity for status change if status was updated
                if (updatedData.status) {
                    await trickleCreateObject(`activity:${leadId}`, {
                        type: 'task',
                        description: `Task "${updatedData.title}" marked as ${updatedData.status}`,
                        createdAt: new Date().toISOString()
                    });
                }

                fetchTasks();
            } catch (error) {
                console.error('Error updating task:', error);
            }
        };

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setNewTask(prev => ({
                ...prev,
                [name]: value
            }));
        };

        return (
            <div data-name="lead-tasks" className="space-y-6">
                <form onSubmit={handleAddTask} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            label="Task Title"
                            name="title"
                            value={newTask.title}
                            onChange={handleInputChange}
                            required
                        />
                        <Input
                            label="Due Date"
                            name="dueDate"
                            type="date"
                            value={newTask.dueDate}
                            onChange={handleInputChange}
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <textarea
                            name="description"
                            value={newTask.description || ''}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Priority
                            </label>
                            <select
                                name="priority"
                                value={newTask.priority}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                    </div>

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            disabled={!newTask.title.trim()}
                        >
                            Add Task
                        </Button>
                    </div>
                </form>

                <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Tasks</h3>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <i className="fas fa-spinner fa-spin text-blue-500"></i>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {tasks.length === 0 ? (
                                <p className="text-gray-500">No tasks yet</p>
                            ) : (
                                tasks.map((task) => (
                                    <div
                                        key={task.objectId}
                                        className={`p-4 rounded-lg border ${
                                            task.objectData.status === 'completed' 
                                                ? 'bg-green-50 border-green-200' 
                                                : task.objectData.status === 'pending'
                                                ? 'bg-yellow-50 border-yellow-200'
                                                : 'bg-gray-50 border-gray-200'
                                        }`}
                                    >
                                        <div className="flex items-start justify-between">
                                            <div>
                                                <h4 className="font-medium">
                                                    {task.objectData.title}
                                                </h4>
                                                {task.objectData.description && (
                                                    <p className="text-sm text-gray-600 mt-1">
                                                        {task.objectData.description}
                                                    </p>
                                                )}
                                                <div className="mt-2 flex items-center space-x-4 text-sm">
                                                    {task.objectData.dueDate && (
                                                        <span className="text-gray-500">
                                                            <i className="fas fa-calendar-alt mr-1"></i>
                                                            Due: {formatDate(task.objectData.dueDate)}
                                                        </span>
                                                    )}
                                                    <span className={`
                                                        px-2 py-1 rounded-full text-xs font-medium
                                                        ${task.objectData.priority === 'high' 
                                                            ? 'bg-red-100 text-red-800'
                                                            : task.objectData.priority === 'medium'
                                                            ? 'bg-yellow-100 text-yellow-800'
                                                            : 'bg-green-100 text-green-800'
                                                        }
                                                    `}>
                                                        {task.objectData.priority.charAt(0).toUpperCase() + task.objectData.priority.slice(1)}
                                                    </span>
                                                </div>
                                            </div>
                                            <div>
                                                <select
                                                    value={task.objectData.status}
                                                    onChange={(e) => handleTaskUpdate(task.objectId, {
                                                        ...task.objectData,
                                                        status: e.target.value
                                                    })}
                                                    className="text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                >
                                                    <option value="pending">Pending</option>
                                                    <option value="in_progress">In Progress</option>
                                                    <option value="completed">Completed</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('LeadTasks component error:', error);
        reportError(error);
        return null;
    }
}
