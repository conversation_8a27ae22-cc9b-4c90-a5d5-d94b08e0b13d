<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    echo json_encode(['step' => 1, 'message' => 'Testing auth handler']);
    
    // Test if auth-handler.php exists
    $authHandlerPath = __DIR__ . '/api/auth-handler.php';
    if (!file_exists($authHandlerPath)) {
        throw new Exception('auth-handler.php not found at: ' . $authHandlerPath);
    }
    
    echo json_encode(['step' => 2, 'message' => 'auth-handler.php exists']);
    
    // Test direct POST to auth-handler
    $postData = json_encode([
        'action' => 'login',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'remember_me' => false
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $url = 'http://localhost/biz/api/auth-handler.php';
    $result = file_get_contents($url, false, $context);
    
    echo json_encode([
        'step' => 3,
        'message' => 'Direct API call completed',
        'response' => $result,
        'http_response_header' => $http_response_header ?? null
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
