// Make LoadingSpinner globally available
window.LoadingSpinner = function LoadingSpinner({ size = 'md', text = 'Loading...', center = true, fullScreen = false, overlay = false }) {
    try {
        // Size variants with border thickness
        const sizeClasses = {
            xs: 'h-3 w-3 border-2',
            sm: 'h-5 w-5 border-2',
            md: 'h-8 w-8 border-2',
            lg: 'h-12 w-12 border-3',
            xl: 'h-16 w-16 border-4'
        };
        
        // Text size based on spinner size
        const textSizes = {
            xs: 'text-xs',
            sm: 'text-xs',
            md: 'text-sm',
            lg: 'text-base',
            xl: 'text-lg'
        };
        
        // Get the appropriate size classes
        const spinnerSize = sizeClasses[size] || sizeClasses.md;
        const textSize = textSizes[size] || textSizes.md;
        
        // Base spinner element
        const spinner = (
            <div className="flex flex-col items-center">
                <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${spinnerSize}`}></div>
                {text && <p className={`mt-2 ${textSize} text-gray-600 text-center`}>{text}</p>}
            </div>
        );
        
        // If fullScreen, render a centered spinner that covers the entire viewport
        if (fullScreen) {
            return (
                <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-75 z-50">
                    {spinner}
                </div>
            );
        }
        
        // If overlay, render a spinner that covers its parent container
        if (overlay) {
            return (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                    {spinner}
                </div>
            );
        }
        
        // If center is true, center the spinner in its container
        if (center) {
            return (
                <div className="flex justify-center items-center p-4">
                    {spinner}
                </div>
            );
        }
        
        // Default: just return the spinner
        return spinner;
    } catch (error) {
        console.error('LoadingSpinner component error:', error);
        // Fallback to a simple loading text if the component fails
        return <div className="text-gray-600">Loading...</div>;
    }
}