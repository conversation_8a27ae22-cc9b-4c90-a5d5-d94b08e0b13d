<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Test database connection directly
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'business_saas';
    
    echo json_encode(['step' => 1, 'message' => 'Testing database connection']);
    
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        throw new Exception('Database connection failed: ' . $conn->connect_error);
    }
    
    echo json_encode(['step' => 2, 'message' => 'Database connected successfully']);
    
    // Test if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result->num_rows == 0) {
        throw new Exception('Users table does not exist');
    }
    
    echo json_encode(['step' => 3, 'message' => 'Users table exists']);
    
    // Test if companies table exists
    $result = $conn->query("SHOW TABLES LIKE 'companies'");
    if ($result->num_rows == 0) {
        throw new Exception('Companies table does not exist');
    }
    
    echo json_encode(['step' => 4, 'message' => 'Companies table exists']);
    
    // Test table structure
    $result = $conn->query("DESCRIBE users");
    $userColumns = [];
    while ($row = $result->fetch_assoc()) {
        $userColumns[] = $row['Field'];
    }
    
    echo json_encode(['step' => 5, 'message' => 'Users table structure', 'columns' => $userColumns]);
    
    $conn->close();
    
    echo json_encode(['success' => true, 'message' => 'Database test completed successfully']);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
