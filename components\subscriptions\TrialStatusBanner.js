// Trial Status Banner Component
function TrialStatusBanner({ authContext, onUpgrade }) {
    const [trialStatus, setTrialStatus] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [showUpgradeModal, setShowUpgradeModal] = React.useState(false);

    React.useEffect(() => {
        fetchTrialStatus();
    }, []);

    const fetchTrialStatus = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/subscription-management/trial-status'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setTrialStatus(data.data);
                }
            }
        } catch (error) {
            console.error('Error fetching trial status:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleUpgradeClick = () => {
        if (onUpgrade) {
            onUpgrade();
        } else {
            setShowUpgradeModal(true);
        }
    };

    const handleExtendTrial = async () => {
        try {
            const response = await fetch(window.getApiUrl('/subscription-management/extend-trial'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    fetchTrialStatus(); // Refresh status
                    // Show success notification
                    if (window.showNotification) {
                        window.showNotification('Trial extended successfully!', 'success');
                    }
                }
            }
        } catch (error) {
            console.error('Error extending trial:', error);
            if (window.showNotification) {
                window.showNotification('Failed to extend trial', 'error');
            }
        }
    };

    if (loading || !trialStatus || !trialStatus.is_trial) {
        return null;
    }

    const { days_remaining, expired, can_extend } = trialStatus;
    const isExpiringSoon = days_remaining <= 3 && !expired;
    const isExpired = expired;

    // Determine banner style based on status
    let bannerClass = 'bg-blue-50 border-blue-200 text-blue-800';
    let iconClass = 'fas fa-info-circle text-blue-500';
    
    if (isExpired) {
        bannerClass = 'bg-red-50 border-red-200 text-red-800';
        iconClass = 'fas fa-exclamation-triangle text-red-500';
    } else if (isExpiringSoon) {
        bannerClass = 'bg-yellow-50 border-yellow-200 text-yellow-800';
        iconClass = 'fas fa-clock text-yellow-500';
    }

    return (
        <>
            <div className={`border-l-4 p-4 mb-6 ${bannerClass}`}>
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <i className={`${iconClass} mr-3 text-lg`}></i>
                        <div>
                            <h3 className="text-sm font-medium">
                                {isExpired ? 'Trial Expired' : 
                                 isExpiringSoon ? 'Trial Expiring Soon' : 
                                 'Free Trial Active'}
                            </h3>
                            <p className="text-sm mt-1">
                                {isExpired ? 
                                    'Your trial has expired. Upgrade now to continue using all features.' :
                                    `Your trial ${days_remaining === 0 ? 'expires today' : `expires in ${days_remaining} day${days_remaining !== 1 ? 's' : ''}`}. Upgrade to continue with full access.`
                                }
                            </p>
                        </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                        {!isExpired && can_extend && (
                            <button
                                onClick={handleExtendTrial}
                                className="text-sm bg-white border border-current rounded-md px-3 py-1 hover:bg-opacity-10 transition-colors"
                            >
                                <i className="fas fa-plus mr-1"></i>
                                Extend Trial
                            </button>
                        )}
                        <button
                            onClick={handleUpgradeClick}
                            className={`text-sm font-medium px-4 py-2 rounded-md transition-colors ${
                                isExpired ?
                                'bg-red-600 text-white hover:bg-red-700' :
                                'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                        >
                            <i className="fas fa-credit-card mr-1"></i>
                            Upgrade Now
                        </button>
                    </div>
                </div>

                {/* Progress bar for trial days */}
                {!isExpired && (
                    <div className="mt-3">
                        <div className="flex justify-between text-xs mb-1">
                            <span>Trial Progress</span>
                            <span>{days_remaining} days remaining</span>
                        </div>
                        <div className="w-full bg-white bg-opacity-50 rounded-full h-2">
                            <div 
                                className={`h-2 rounded-full transition-all duration-300 ${
                                    isExpiringSoon ? 'bg-yellow-400' : 'bg-blue-400'
                                }`}
                                style={{ 
                                    width: `${Math.max(5, ((14 - days_remaining) / 14) * 100)}%` 
                                }}
                            ></div>
                        </div>
                    </div>
                )}
            </div>

            {/* Upgrade Modal */}
            {showUpgradeModal && (
                <UpgradeModal
                    isOpen={showUpgradeModal}
                    onClose={() => setShowUpgradeModal(false)}
                    authContext={authContext}
                    trialStatus={trialStatus}
                />
            )}
        </>
    );
}

// Simple Upgrade Modal Component
function UpgradeModal({ isOpen, onClose, authContext, trialStatus }) {
    const [plans, setPlans] = React.useState([]);
    const [selectedPlan, setSelectedPlan] = React.useState(null);
    const [billingCycle, setBillingCycle] = React.useState('monthly');
    const [loading, setLoading] = React.useState(false);

    React.useEffect(() => {
        if (isOpen) {
            fetchPlans();
        }
    }, [isOpen]);

    const fetchPlans = async () => {
        try {
            const response = await fetch(window.getApiUrl('/pricing-plans'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    const visiblePlans = data.data.filter(plan => plan.is_visible && plan.id !== 'trial');
                    setPlans(visiblePlans);
                    if (visiblePlans.length > 0) {
                        setSelectedPlan(visiblePlans.find(p => p.is_popular) || visiblePlans[0]);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching plans:', error);
        }
    };

    const handleUpgrade = () => {
        if (!selectedPlan) return;

        // Show payment modal instead of direct upgrade
        setShowPaymentModal(true);
    };

    const [showPaymentModal, setShowPaymentModal] = React.useState(false);

    const handlePaymentSuccess = (paymentData) => {
        if (window.showNotification) {
            window.showNotification('Payment successful! Your plan has been upgraded.', 'success');
        }
        setShowPaymentModal(false);
        onClose();
        // Refresh the page to update subscription status
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    };

    const handlePaymentError = (error) => {
        if (window.showNotification) {
            window.showNotification(`Payment failed: ${error}`, 'error');
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
                <div className="mt-3">
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Upgrade Your Plan
                        </h3>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <i className="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    {/* Billing Cycle Toggle */}
                    <div className="flex justify-center mb-6">
                        <div className="bg-gray-100 p-1 rounded-lg">
                            <button
                                onClick={() => setBillingCycle('monthly')}
                                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                    billingCycle === 'monthly' 
                                        ? 'bg-white text-gray-900 shadow-sm' 
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                Monthly
                            </button>
                            <button
                                onClick={() => setBillingCycle('yearly')}
                                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                                    billingCycle === 'yearly' 
                                        ? 'bg-white text-gray-900 shadow-sm' 
                                        : 'text-gray-600 hover:text-gray-900'
                                }`}
                            >
                                Yearly
                                <span className="ml-1 text-xs text-green-600">(Save 17%)</span>
                            </button>
                        </div>
                    </div>

                    {/* Plan Selection */}
                    <div className="space-y-4 mb-6">
                        {plans.map((plan) => {
                            const price = billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;
                            const isSelected = selectedPlan?.id === plan.id;
                            
                            return (
                                <div
                                    key={plan.id}
                                    onClick={() => setSelectedPlan(plan)}
                                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                                        isSelected 
                                            ? 'border-blue-500 bg-blue-50' 
                                            : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                >
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <h4 className="text-lg font-semibold text-gray-900">
                                                {plan.name}
                                                {plan.is_popular && (
                                                    <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                                        Popular
                                                    </span>
                                                )}
                                            </h4>
                                            <p className="text-gray-600 text-sm">{plan.short_description}</p>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-2xl font-bold text-gray-900">
                                                ₹{price.toLocaleString()}
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                /{billingCycle === 'yearly' ? 'year' : 'month'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                            disabled={loading}
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleUpgrade}
                            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                            disabled={!selectedPlan}
                        >
                            <i className="fas fa-credit-card mr-2"></i>
                            {`Pay ${billingCycle === 'yearly' ? '₹' + (selectedPlan?.price_yearly || 0).toLocaleString() : '₹' + (selectedPlan?.price_monthly || 0).toLocaleString()}`}
                        </button>
                    </div>
                </div>
            </div>

            {/* Payment Modal */}
            {showPaymentModal && selectedPlan && (
                <UpgradePaymentModal
                    isOpen={showPaymentModal}
                    onClose={() => setShowPaymentModal(false)}
                    planData={selectedPlan}
                    billingCycle={billingCycle}
                    userInfo={authContext.user}
                    onSuccess={handlePaymentSuccess}
                />
            )}
        </div>
    );
}

// Make components globally available
window.TrialStatusBanner = TrialStatusBanner;
window.UpgradeModal = UpgradeModal;
