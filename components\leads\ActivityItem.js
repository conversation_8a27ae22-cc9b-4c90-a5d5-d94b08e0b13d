function ActivityItem({ activity }) {
    try {
        const getActivityIcon = () => {
            switch (activity.objectData.type) {
                case 'note':
                    return 'fa-sticky-note';
                case 'call':
                    return 'fa-phone';
                case 'email':
                    return 'fa-envelope';
                case 'meeting':
                    return 'fa-users';
                case 'task':
                    return 'fa-tasks';
                case 'status_change':
                    return 'fa-exchange-alt';
                default:
                    return 'fa-clock';
            }
        };

        const getActivityColor = () => {
            switch (activity.objectData.type) {
                case 'note':
                    return 'bg-green-100 text-green-600';
                case 'call':
                    return 'bg-blue-100 text-blue-600';
                case 'email':
                    return 'bg-purple-100 text-purple-600';
                case 'meeting':
                    return 'bg-yellow-100 text-yellow-600';
                case 'task':
                    return 'bg-indigo-100 text-indigo-600';
                case 'status_change':
                    return 'bg-red-100 text-red-600';
                default:
                    return 'bg-gray-100 text-gray-600';
            }
        };

        return (
            <div className="lead-activity-item">
                <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor()}`}>
                        <i className={`fas ${getActivityIcon()}`}></i>
                    </div>
                    <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                            {activity.objectData.description}
                        </p>
                        <p className="text-xs text-gray-500">
                            {formatDateTime(activity.createdAt)}
                        </p>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ActivityItem component error:', error);
        reportError(error);
        return null;
    }
}
