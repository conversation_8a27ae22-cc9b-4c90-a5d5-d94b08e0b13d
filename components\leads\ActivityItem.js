function ActivityItem({ activity, onEdit, onDelete }) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [editData, setEditData] = React.useState({
        type: activity.objectData.type,
        description: activity.objectData.description
    });

    try {
        const getActivityIcon = () => {
            switch (activity.objectData.type) {
                case 'note':
                    return 'fa-sticky-note';
                case 'call':
                    return 'fa-phone';
                case 'email':
                    return 'fa-envelope';
                case 'meeting':
                    return 'fa-users';
                case 'task':
                    return 'fa-tasks';
                case 'status_change':
                    return 'fa-exchange-alt';
                default:
                    return 'fa-clock';
            }
        };

        const getActivityColor = () => {
            switch (activity.objectData.type) {
                case 'note':
                    return 'bg-green-100 text-green-600';
                case 'call':
                    return 'bg-blue-100 text-blue-600';
                case 'email':
                    return 'bg-purple-100 text-purple-600';
                case 'meeting':
                    return 'bg-yellow-100 text-yellow-600';
                case 'task':
                    return 'bg-indigo-100 text-indigo-600';
                case 'status_change':
                    return 'bg-red-100 text-red-600';
                default:
                    return 'bg-gray-100 text-gray-600';
            }
        };

        const handleSave = () => {
            if (onEdit) {
                onEdit(activity.objectId, editData);
            }
            setIsEditing(false);
        };

        const handleCancel = () => {
            setEditData({
                type: activity.objectData.type,
                description: activity.objectData.description
            });
            setIsEditing(false);
        };

        const handleDelete = () => {
            if (onDelete && window.confirm('Are you sure you want to delete this activity?')) {
                onDelete(activity.objectId);
            }
        };

        return (
            <div className="lead-activity-item border-b border-gray-100 pb-4 mb-4">
                <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor()}`}>
                        <i className={`fas ${getActivityIcon()}`}></i>
                    </div>
                    <div className="flex-1 min-w-0">
                        {isEditing ? (
                            <div className="space-y-3">
                                <div>
                                    <select
                                        value={editData.type}
                                        onChange={(e) => setEditData(prev => ({ ...prev, type: e.target.value }))}
                                        className="text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        <option value="note">Note</option>
                                        <option value="call">Call</option>
                                        <option value="email">Email</option>
                                        <option value="meeting">Meeting</option>
                                        <option value="task">Task</option>
                                        <option value="status_change">Status Change</option>
                                    </select>
                                </div>
                                <div>
                                    <textarea
                                        value={editData.description}
                                        onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                                        rows={2}
                                        className="w-full text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={handleSave}
                                        className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                                    >
                                        Save
                                    </button>
                                    <button
                                        onClick={handleCancel}
                                        className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        ) : (
                            <>
                                <p className="text-sm font-medium text-gray-900">
                                    {activity.objectData.description}
                                </p>
                                <p className="text-xs text-gray-500">
                                    {formatDateTime(activity.createdAt)}
                                </p>
                            </>
                        )}
                    </div>
                    {!isEditing && (
                        <div className="flex space-x-1">
                            <button
                                onClick={() => setIsEditing(true)}
                                className="text-gray-400 hover:text-blue-600 text-xs"
                                title="Edit"
                            >
                                <i className="fas fa-edit"></i>
                            </button>
                            <button
                                onClick={handleDelete}
                                className="text-gray-400 hover:text-red-600 text-xs"
                                title="Delete"
                            >
                                <i className="fas fa-trash"></i>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('ActivityItem component error:', error);
        reportError(error);
        return null;
    }
}
