/**
 * Main utility loader
 * This file loads all utility files in the correct order
 */

// Function to load a script dynamically with retry
function loadScript(src, retries = 3, delay = 1000) {
    return new Promise((resolve, reject) => {
        const attemptLoad = (attemptsLeft) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve();
            script.onerror = (e) => {
                console.warn(`Failed to load script: ${src}, attempts left: ${attemptsLeft}`);
                
                if (attemptsLeft > 0) {
                    // Try alternative path if it's a utility script
                    if (src.includes('/utils/') && attemptsLeft === retries - 1) {
                        // Try without basePath
                        const altSrc = src.replace(basePath, '');
                        console.log(`Trying alternative path: ${altSrc}`);
                        setTimeout(() => attemptLoad(attemptsLeft - 1, altSrc), delay);
                    } else {
                        setTimeout(() => attemptLoad(attemptsLeft - 1), delay);
                    }
                } else {
                    // Create a fallback for common utilities
                    if (src.includes('LoadingSpinner.js')) {
                        createLoadingSpinnerFallback();
                        resolve(); // Resolve anyway since we have a fallback
                    } else if (src.includes('ErrorMessage.js')) {
                        createErrorMessageFallback();
                        resolve(); // Resolve anyway since we have a fallback
                    } else {
                        reject(new Error(`Failed to load script after ${retries} attempts: ${src}`));
                    }
                }
            };
            document.head.appendChild(script);
        };
        
        attemptLoad(retries);
    });
}

// Create fallbacks for critical components
function createLoadingSpinnerFallback() {
    console.warn('Creating LoadingSpinner fallback');
    window.LoadingSpinner = function({ text = 'Loading...', size = 'md' }) {
        return React.createElement('div', { 
            className: 'flex flex-col items-center justify-center p-4' 
        }, [
            React.createElement('div', { 
                key: 'spinner',
                className: 'animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600'
            }),
            React.createElement('p', { 
                key: 'text',
                className: 'mt-2 text-gray-600' 
            }, text)
        ]);
    };
}

function createErrorMessageFallback() {
    console.warn('Creating ErrorMessage fallback');
    window.ErrorMessage = function({ error, onRetry }) {
        const message = typeof error === 'string' ? error : (error?.message || 'An error occurred');
        return React.createElement('div', { 
            className: 'bg-red-50 border border-red-200 rounded-md p-4 my-4' 
        }, [
            React.createElement('p', { 
                key: 'message',
                className: 'text-red-700' 
            }, message),
            onRetry && React.createElement('button', { 
                key: 'retry',
                onClick: onRetry,
                className: 'mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200'
            }, 'Try Again')
        ]);
    };
}

// Get the base path from APP_CONFIG or use a default for XAMPP setup
const basePath = window.APP_CONFIG ? window.APP_CONFIG.BASE_PATH : '/biz';

// List of utility scripts to load in order
const utilityScripts = [
    basePath + '/utils/ValidationUtils.js',
    basePath + '/utils/dataCache.js',
    basePath + '/utils/NotificationManager.js',
    basePath + '/utils/NetworkStatus.js',
    basePath + '/utils/ApiClient.js',
    basePath + '/components/common/LoadingSpinner.js',
    basePath + '/components/common/ErrorMessage.js',
    basePath + '/components/common/NotificationContainer.js',
    basePath + '/components/subscriptions/TrialBanner.js',
    basePath + '/components/subscriptions/ExtendTrialModal.js'
];

// Load all utility scripts
async function loadUtilities() {
    console.log('Loading utility scripts...');
    let loadedCount = 0;
    let failedScripts = [];
    
    // Create a notification function if it doesn't exist
    if (!window.showNotification && typeof React !== 'undefined') {
        window.showNotification = function(options) {
            console.log(`Notification: ${options.type} - ${options.message}`);
        };
    }
    
    // Load each script and continue even if some fail
    for (const script of utilityScripts) {
        try {
            await loadScript(script);
            console.log(`✅ Loaded: ${script}`);
            loadedCount++;
        } catch (error) {
            console.error(`❌ Failed to load: ${script}`, error);
            failedScripts.push(script);
        }
    }
    
    // Report loading status
    if (failedScripts.length === 0) {
        console.log('✅ All utility scripts loaded successfully');
    } else {
        console.warn(`⚠️ Loaded ${loadedCount}/${utilityScripts.length} scripts. Failed: ${failedScripts.join(', ')}`);
        
        // Create fallbacks for critical components if they failed to load
        if (!window.LoadingSpinner) createLoadingSpinnerFallback();
        if (!window.ErrorMessage) createErrorMessageFallback();
        if (!window.NotificationManager && !window.showNotification) {
            window.showNotification = function(options) {
                alert(`${options.type}: ${options.message}`);
            };
        }
    }
    
    // Initialize any utilities that need initialization
    if (window.dataCache) {
        // Clear expired items from cache on startup
        window.dataCache.clearExpired();
    }
    
    // Dispatch an event to notify the application that utilities are loaded
    window.dispatchEvent(new CustomEvent('utilities-loaded', { 
        detail: { 
            success: failedScripts.length === 0,
            loaded: loadedCount,
            total: utilityScripts.length,
            failed: failedScripts
        } 
    }));
}

// Load utilities when the document is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadUtilities);
} else {
    loadUtilities();
}