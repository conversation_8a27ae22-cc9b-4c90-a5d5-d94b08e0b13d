// Business Type Templates Management Component
function BusinessTypeTemplates({ authContext, setNotification }) {
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [selectedType, setSelectedType] = React.useState(null);
    const [templates, setTemplates] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [editingTemplate, setEditingTemplate] = React.useState(null);
    const [showTemplateModal, setShowTemplateModal] = React.useState(false);

    React.useEffect(() => {
        fetchBusinessTypes();
    }, []);

    React.useEffect(() => {
        if (selectedType) {
            fetchTemplates(selectedType.id);
        }
    }, [selectedType]);

    const fetchBusinessTypes = async () => {
        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/business-types'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setBusinessTypes(data.data || []);
                    if (data.data && data.data.length > 0) {
                        setSelectedType(data.data[0]);
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching business types:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load business types'
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchTemplates = async (businessTypeId) => {
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/business-types/${businessTypeId}/templates`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setTemplates(data.data || []);
                }
            }
        } catch (error) {
            console.error('Error fetching templates:', error);
        }
    };

    const handleCreateTemplate = () => {
        setEditingTemplate({
            business_type_id: selectedType.id,
            name: '',
            description: '',
            categories: [],
            subcategories: {},
            items: {},
            is_default: false
        });
        setShowTemplateModal(true);
    };

    const handleEditTemplate = (template) => {
        setEditingTemplate({
            ...template,
            categories: Array.isArray(template.categories) ? template.categories : [],
            subcategories: typeof template.subcategories === 'object' ? template.subcategories : {},
            items: typeof template.items === 'object' ? template.items : {}
        });
        setShowTemplateModal(true);
    };

    const handleSaveTemplate = async () => {
        try {
            const url = editingTemplate.id 
                ? window.getApiUrl(`/super-admin/business-types/templates/${editingTemplate.id}`)
                : window.getApiUrl('/super-admin/business-types/templates');
            
            const method = editingTemplate.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editingTemplate)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: `Template ${editingTemplate.id ? 'updated' : 'created'} successfully`
                    });
                    setShowTemplateModal(false);
                    setEditingTemplate(null);
                    fetchTemplates(selectedType.id);
                } else {
                    throw new Error(data.message || 'Failed to save template');
                }
            } else {
                throw new Error('Failed to save template');
            }
        } catch (error) {
            console.error('Error saving template:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to save template'
            });
        }
    };

    const handleDeleteTemplate = async (templateId) => {
        if (!confirm('Are you sure you want to delete this template?')) return;

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/business-types/templates/${templateId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setNotification({
                        type: 'success',
                        message: 'Template deleted successfully'
                    });
                    fetchTemplates(selectedType.id);
                } else {
                    throw new Error(data.message || 'Failed to delete template');
                }
            } else {
                throw new Error('Failed to delete template');
            }
        } catch (error) {
            console.error('Error deleting template:', error);
            setNotification({
                type: 'error',
                message: error.message || 'Failed to delete template'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">Business Type Templates</h3>
                    <p className="text-sm text-gray-600">Configure default templates for each business type</p>
                </div>
                <button
                    onClick={handleCreateTemplate}
                    disabled={!selectedType}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Add Template
                </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {/* Business Types Sidebar */}
                <div className="lg:col-span-1">
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-4 py-3 border-b border-gray-200">
                            <h4 className="text-sm font-medium text-gray-900">Business Types</h4>
                        </div>
                        <div className="p-2">
                            {businessTypes.map((type) => (
                                <button
                                    key={type.id}
                                    onClick={() => setSelectedType(type)}
                                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                                        selectedType?.id === type.id
                                            ? 'bg-blue-100 text-blue-700'
                                            : 'text-gray-700 hover:bg-gray-100'
                                    }`}
                                >
                                    <div className="flex items-center">
                                        <i className={`${type.icon} mr-2`}></i>
                                        <span className="truncate">{type.name}</span>
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Templates Content */}
                <div className="lg:col-span-3">
                    {selectedType ? (
                        <div className="space-y-4">
                            <div className="bg-white rounded-lg shadow p-6">
                                <div className="flex items-center mb-4">
                                    <i className={`${selectedType.icon} text-2xl text-${selectedType.color}-600 mr-3`}></i>
                                    <div>
                                        <h4 className="text-lg font-medium text-gray-900">{selectedType.name}</h4>
                                        <p className="text-sm text-gray-600">{selectedType.description}</p>
                                    </div>
                                </div>

                                {/* Templates List */}
                                <div className="space-y-4">
                                    <h5 className="text-md font-medium text-gray-900">Templates ({templates.length})</h5>
                                    
                                    {templates.length > 0 ? (
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {templates.map((template) => (
                                                <div key={template.id} className="border border-gray-200 rounded-lg p-4">
                                                    <div className="flex justify-between items-start mb-2">
                                                        <div>
                                                            <h6 className="text-sm font-medium text-gray-900">{template.name}</h6>
                                                            <p className="text-xs text-gray-600">{template.description}</p>
                                                        </div>
                                                        <div className="flex space-x-2">
                                                            <button
                                                                onClick={() => handleEditTemplate(template)}
                                                                className="text-blue-600 hover:text-blue-800"
                                                                title="Edit"
                                                            >
                                                                <i className="fas fa-edit text-sm"></i>
                                                            </button>
                                                            <button
                                                                onClick={() => handleDeleteTemplate(template.id)}
                                                                className="text-red-600 hover:text-red-800"
                                                                title="Delete"
                                                            >
                                                                <i className="fas fa-trash text-sm"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    
                                                    <div className="text-xs text-gray-500">
                                                        <div>Categories: {template.categories?.length || 0}</div>
                                                        <div>Subcategories: {Object.keys(template.subcategories || {}).length}</div>
                                                        <div>Items: {Object.keys(template.items || {}).length}</div>
                                                        {template.is_default && (
                                                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 mt-1">
                                                                Default
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <div className="text-gray-400 mb-2">
                                                <i className="fas fa-file-alt text-3xl"></i>
                                            </div>
                                            <h6 className="text-sm font-medium text-gray-900 mb-1">No Templates</h6>
                                            <p className="text-xs text-gray-600 mb-4">
                                                Create templates to auto-configure new companies of this business type.
                                            </p>
                                            <button
                                                onClick={handleCreateTemplate}
                                                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                                            >
                                                Create First Template
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="bg-white rounded-lg shadow p-6 text-center">
                            <div className="text-gray-400 mb-4">
                                <i className="fas fa-arrow-left text-3xl"></i>
                            </div>
                            <h4 className="text-lg font-medium text-gray-900 mb-2">Select a Business Type</h4>
                            <p className="text-gray-600">Choose a business type from the sidebar to manage its templates.</p>
                        </div>
                    )}
                </div>
            </div>

            {/* Template Modal */}
            {showTemplateModal && editingTemplate && (
                <BusinessTypeTemplateModal
                    template={editingTemplate}
                    isOpen={showTemplateModal}
                    onClose={() => setShowTemplateModal(false)}
                    onSave={handleSaveTemplate}
                    setTemplate={setEditingTemplate}
                />
            )}
        </div>
    );
}

// Business Type Template Modal Component
function BusinessTypeTemplateModal({ template, isOpen, onClose, onSave, setTemplate }) {
    const [newCategory, setNewCategory] = React.useState('');
    const [newSubcategory, setNewSubcategory] = React.useState('');
    const [selectedCategory, setSelectedCategory] = React.useState('');
    const [newItem, setNewItem] = React.useState('');
    const [selectedSubcategory, setSelectedSubcategory] = React.useState('');

    const addCategory = () => {
        if (newCategory.trim() && !template.categories.includes(newCategory.trim())) {
            setTemplate({
                ...template,
                categories: [...template.categories, newCategory.trim()]
            });
            setNewCategory('');
        }
    };

    const removeCategory = (categoryToRemove) => {
        const updatedCategories = template.categories.filter(cat => cat !== categoryToRemove);
        const updatedSubcategories = { ...template.subcategories };
        const updatedItems = { ...template.items };

        // Remove subcategories and items for this category
        delete updatedSubcategories[categoryToRemove];
        Object.keys(updatedItems).forEach(key => {
            if (key.startsWith(categoryToRemove + '.')) {
                delete updatedItems[key];
            }
        });

        setTemplate({
            ...template,
            categories: updatedCategories,
            subcategories: updatedSubcategories,
            items: updatedItems
        });
    };

    const addSubcategory = () => {
        if (newSubcategory.trim() && selectedCategory) {
            const currentSubs = template.subcategories[selectedCategory] || [];
            if (!currentSubs.includes(newSubcategory.trim())) {
                setTemplate({
                    ...template,
                    subcategories: {
                        ...template.subcategories,
                        [selectedCategory]: [...currentSubs, newSubcategory.trim()]
                    }
                });
                setNewSubcategory('');
            }
        }
    };

    const removeSubcategory = (category, subcategoryToRemove) => {
        const updatedSubcategories = { ...template.subcategories };
        const updatedItems = { ...template.items };

        updatedSubcategories[category] = updatedSubcategories[category].filter(sub => sub !== subcategoryToRemove);

        // Remove items for this subcategory
        const itemKey = `${category}.${subcategoryToRemove}`;
        delete updatedItems[itemKey];

        setTemplate({
            ...template,
            subcategories: updatedSubcategories,
            items: updatedItems
        });
    };

    const addItem = () => {
        if (newItem.trim() && selectedSubcategory) {
            const itemKey = selectedSubcategory;
            const currentItems = template.items[itemKey] || [];
            if (!currentItems.includes(newItem.trim())) {
                setTemplate({
                    ...template,
                    items: {
                        ...template.items,
                        [itemKey]: [...currentItems, newItem.trim()]
                    }
                });
                setNewItem('');
            }
        }
    };

    const removeItem = (itemKey, itemToRemove) => {
        const updatedItems = { ...template.items };
        updatedItems[itemKey] = updatedItems[itemKey].filter(item => item !== itemToRemove);

        setTemplate({
            ...template,
            items: updatedItems
        });
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-bold text-gray-900">
                        {template.id ? 'Edit Template' : 'Create Template'}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <i className="fas fa-times"></i>
                    </button>
                </div>

                <div className="space-y-6 max-h-96 overflow-y-auto">
                    {/* Basic Info */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                            <input
                                type="text"
                                value={template.name}
                                onChange={(e) => setTemplate({...template, name: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="e.g., Digital Marketing Default"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <input
                                type="text"
                                value={template.description}
                                onChange={(e) => setTemplate({...template, description: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Brief description"
                            />
                        </div>
                    </div>

                    <div className="flex items-center">
                        <input
                            type="checkbox"
                            id="is_default"
                            checked={template.is_default}
                            onChange={(e) => setTemplate({...template, is_default: e.target.checked})}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="is_default" className="ml-2 block text-sm text-gray-900">
                            Set as default template for this business type
                        </label>
                    </div>

                    {/* Categories */}
                    <div>
                        <h4 className="text-md font-medium text-gray-900 mb-3">Categories</h4>
                        <div className="flex gap-2 mb-3">
                            <input
                                type="text"
                                value={newCategory}
                                onChange={(e) => setNewCategory(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && addCategory()}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Add category (e.g., SEO, Social Media)"
                            />
                            <button
                                onClick={addCategory}
                                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                            >
                                Add
                            </button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                            {template.categories.map((category, index) => (
                                <span
                                    key={index}
                                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                                >
                                    {category}
                                    <button
                                        onClick={() => removeCategory(category)}
                                        className="ml-2 text-blue-600 hover:text-blue-800"
                                    >
                                        <i className="fas fa-times text-xs"></i>
                                    </button>
                                </span>
                            ))}
                        </div>
                    </div>

                    {/* Subcategories */}
                    {template.categories.length > 0 && (
                        <div>
                            <h4 className="text-md font-medium text-gray-900 mb-3">Subcategories</h4>
                            <div className="flex gap-2 mb-3">
                                <select
                                    value={selectedCategory}
                                    onChange={(e) => setSelectedCategory(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Category</option>
                                    {template.categories.map((category) => (
                                        <option key={category} value={category}>{category}</option>
                                    ))}
                                </select>
                                <input
                                    type="text"
                                    value={newSubcategory}
                                    onChange={(e) => setNewSubcategory(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && addSubcategory()}
                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Add subcategory"
                                    disabled={!selectedCategory}
                                />
                                <button
                                    onClick={addSubcategory}
                                    disabled={!selectedCategory}
                                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
                                >
                                    Add
                                </button>
                            </div>

                            {Object.entries(template.subcategories).map(([category, subcategories]) => (
                                <div key={category} className="mb-3">
                                    <h5 className="text-sm font-medium text-gray-700 mb-2">{category}</h5>
                                    <div className="flex flex-wrap gap-2 ml-4">
                                        {subcategories.map((subcategory, index) => (
                                            <span
                                                key={index}
                                                className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800"
                                            >
                                                {subcategory}
                                                <button
                                                    onClick={() => removeSubcategory(category, subcategory)}
                                                    className="ml-1 text-green-600 hover:text-green-800"
                                                >
                                                    <i className="fas fa-times text-xs"></i>
                                                </button>
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="flex justify-end space-x-4 mt-6 pt-4 border-t">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onSave}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        {template.id ? 'Update' : 'Create'}
                    </button>
                </div>
            </div>
        </div>
    );
}

// Make components globally available
window.BusinessTypeTemplates = BusinessTypeTemplates;
window.BusinessTypeTemplateModal = BusinessTypeTemplateModal;
