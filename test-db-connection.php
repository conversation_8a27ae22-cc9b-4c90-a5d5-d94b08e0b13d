<?php
header('Content-Type: application/json');

try {
    // Load configuration
    require_once __DIR__ . '/config/config.php';
    Config::load();
    
    $host = Config::get('DB_HOST', 'localhost');
    $username = Config::get('DB_USERNAME', 'root');
    $password = Config::get('DB_PASSWORD', '');
    $database = Config::get('DB_NAME', 'business_saas');
    
    echo json_encode([
        'config_loaded' => true,
        'db_host' => $host,
        'db_name' => $database,
        'db_username' => $username
    ]);
    
    // Test database connection
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $usersTableExists = $stmt->rowCount() > 0;
    
    // Get table structure if it exists
    $tableStructure = [];
    if ($usersTableExists) {
        $stmt = $pdo->query("DESCRIBE users");
        $tableStructure = $stmt->fetchAll();
    }
    
    echo json_encode([
        'success' => true,
        'database_connected' => true,
        'users_table_exists' => $usersTableExists,
        'table_structure' => $tableStructure,
        'config' => [
            'host' => $host,
            'database' => $database,
            'username' => $username
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
