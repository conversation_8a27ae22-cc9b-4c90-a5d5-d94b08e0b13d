/**
 * Application Configuration - Production Version
 */

// Determine environment
const isLocalhost = window.location.hostname === 'localhost';
const isXamppSetup = window.location.pathname.includes('/biz');

window.APP_CONFIG = {
    // API Configuration
    API_BASE_URL: isXamppSetup ? '/biz/api/api.php' : '/api/api.php',  // Adjust for XAMPP setup

    // Base path for the application
    BASE_PATH: isXamppSetup ? '/biz' : '',  // Adjust for XAMPP setup

    // Application Settings
    APP_NAME: 'Business Management SaaS',
    VERSION: '1.0.0',
    
    // Feature Flags
    FEATURES: {
        SUPER_ADMIN: true,
        SUBSCRIPTIONS: true,
        PAYMENTS: true,
        REPORTS: true
    },
    
    // Default Settings
    DEFAULTS: {
        PAGINATION_LIMIT: 50,
        CURRENCY: 'INR',
        DATE_FORMAT: 'DD/MM/YYYY',
        TIMEZONE: 'Asia/Kolkata'
    }
};

// Helper function to get API URL
window.getApiUrl = function(endpoint) {
    const baseUrl = window.APP_CONFIG.API_BASE_URL;
    return endpoint.startsWith('/') ? baseUrl + endpoint : baseUrl + '/' + endpoint;
};

// Helper function to get application URL with correct base path
window.getAppUrl = function(path) {
    const basePath = window.APP_CONFIG.BASE_PATH;
    const cleanPath = path.startsWith('/') ? path : '/' + path;
    return basePath + cleanPath;
};