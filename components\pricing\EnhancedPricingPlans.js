// Enhanced Pricing Plans Component with Database Integration
function EnhancedPricingPlans({ onPlanSelect, showTitle = true, selectedPlan = null }) {
    const [billingCycle, setBillingCycle] = React.useState('monthly');
    const [plans, setPlans] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [error, setError] = React.useState(null);

    React.useEffect(() => {
        fetchPlans();
    }, []);

    const fetchPlans = async () => {
        try {
            setLoading(true);
            const apiUrl = window.getApiUrl ? window.getApiUrl('/pricing-plans') : '/biz/api/pricing-plans.php';
            const response = await fetch(apiUrl);
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setPlans(data.data || []);
                } else {
                    setError(data.message || 'Failed to load plans');
                }
            } else {
                setError('Failed to fetch plans');
            }
        } catch (error) {
            console.error('Error fetching plans:', error);
            setError('Network error while fetching plans');
            // Fallback to default plan
            setPlans([{
                id: 'basic',
                name: 'Business Plan',
                short_description: 'Complete business management solution',
                price_monthly: 500,
                price_yearly: 5000,
                trial_days: 14,
                features: [
                    'Customer Management',
                    'Invoice Generation',
                    'Quotation Management',
                    'Contract Management',
                    'Lead Tracking',
                    'Business Analytics',
                    'Email Notifications',
                    'Data Export',
                    'Multi-user Access',
                    '24/7 Support'
                ],
                is_popular: true,
                is_visible: true,
                yearly_savings: 1000,
                yearly_savings_percentage: 17
            }]);
        } finally {
            setLoading(false);
        }
    };

    const handlePlanSelect = (plan) => {
        if (onPlanSelect) {
            const price = billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;
            onPlanSelect({
                planId: plan.id,
                plan: plan.id,
                cycle: billingCycle,
                billingCycle: billingCycle,
                price: price,
                planData: plan
            });
        }
    };

    const getPrice = (plan) => {
        return billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;
    };

    const getSavings = (plan) => {
        if (billingCycle === 'yearly' && plan.yearly_savings > 0) {
            return plan.yearly_savings;
        }
        return 0;
    };

    if (loading) {
        return (
            <div className="max-w-6xl mx-auto px-4 py-8">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading pricing plans...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="max-w-6xl mx-auto px-4 py-8">
                <div className="text-center">
                    <div className="text-red-600 mb-4">
                        <i className="fas fa-exclamation-triangle text-2xl"></i>
                    </div>
                    <p className="text-red-600">{error}</p>
                    <button 
                        onClick={fetchPlans}
                        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-6xl mx-auto px-4 py-8">
            {showTitle && (
                <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                        Simple, Transparent Pricing
                    </h2>
                    <p className="text-lg text-gray-600 mb-2">
                        Start with our 14-day free trial. No credit card required.
                    </p>
                    <div className="flex justify-center items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                            <i className="fas fa-check text-green-500"></i>
                            <span>Cancel anytime</span>
                        </div>
                        <div className="flex items-center space-x-1">
                            <i className="fas fa-shield-alt text-green-500"></i>
                            <span>Secure payments</span>
                        </div>
                        <div className="flex items-center space-x-1">
                            <i className="fas fa-headset text-green-500"></i>
                            <span>24/7 support</span>
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced Billing Toggle */}
            <PricingToggle
                billingCycle={billingCycle}
                onCycleChange={setBillingCycle}
                plans={plans}
            />

            {/* Plans Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {plans.map((plan) => {
                    const price = getPrice(plan);
                    const savings = getSavings(plan);
                    const isSelected = selectedPlan === plan.id;
                    
                    return (
                        <div
                            key={plan.id}
                            className={`relative bg-white rounded-lg shadow-lg border-2 transition-all duration-200 ${
                                plan.is_popular
                                    ? 'border-blue-500 transform scale-105'
                                    : isSelected
                                    ? 'border-blue-400'
                                    : 'border-gray-200 hover:border-gray-300'
                            }`}
                        >
                            {/* Popular Badge */}
                            {plan.is_popular && (
                                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                                        Most Popular
                                    </span>
                                </div>
                            )}

                            <div className="p-6">
                                {/* Plan Header */}
                                <div className="text-center mb-6">
                                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                                        {plan.name}
                                    </h3>
                                    <p className="text-gray-600 text-sm">
                                        {plan.short_description || plan.description}
                                    </p>
                                </div>

                                {/* Enhanced Pricing Display */}
                                <div className="mb-6">
                                    <PricingSummary
                                        plan={plan}
                                        billingCycle={billingCycle}
                                        showComparison={false}
                                    />
                                    {plan.trial_days > 0 && (
                                        <p className="text-blue-600 text-sm mt-2 text-center">
                                            {plan.trial_days}-day free trial
                                        </p>
                                    )}
                                </div>

                                {/* Features */}
                                <div className="mb-6">
                                    <ul className="space-y-3">
                                        {(plan.features || []).map((feature, index) => (
                                            <li key={index} className="flex items-start">
                                                <i className="fas fa-check text-green-500 mt-1 mr-3 text-sm"></i>
                                                <span className="text-gray-700 text-sm">{feature}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>

                                {/* CTA Button */}
                                <button
                                    onClick={() => handlePlanSelect(plan)}
                                    className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
                                        plan.is_popular
                                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                                            : isSelected
                                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                                            : 'bg-gray-50 text-gray-900 border border-gray-300 hover:bg-gray-100'
                                    }`}
                                >
                                    {isSelected ? (
                                        <>
                                            <i className="fas fa-check mr-2"></i>
                                            Selected
                                        </>
                                    ) : (
                                        plan.is_popular ? 'Start Free Trial' : 'Choose Plan'
                                    )}
                                </button>
                            </div>
                        </div>
                    );
                })}
            </div>

            {/* Additional Info */}
            <div className="text-center mt-8">
                <p className="text-gray-600 text-sm">
                    All plans include 24/7 support and can be cancelled anytime.
                </p>
                <p className="text-gray-600 text-sm mt-1">
                    Need a custom solution? <a href="#contact" className="text-blue-600 hover:text-blue-500">Contact us</a>
                </p>
            </div>
        </div>
    );
}

// Make component globally available
window.EnhancedPricingPlans = EnhancedPricingPlans;
