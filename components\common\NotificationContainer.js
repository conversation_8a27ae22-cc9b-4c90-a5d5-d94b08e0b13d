function NotificationContainer() {
    try {
        const [notifications, setNotifications] = React.useState([]);
        
        React.useEffect(() => {
            // Check if NotificationManager is available
            if (!window.NotificationManager) {
                console.error('NotificationManager not found');
                return;
            }
            
            // Add listener for notifications
            const removeListener = window.NotificationManager.addListener(setNotifications);
            
            // Clean up listener on unmount
            return () => removeListener();
        }, []);
        
        // If no notifications, don't render anything
        if (!notifications.length) return null;
        
        return (
            <div className="fixed top-4 right-4 z-50 w-full max-w-sm space-y-4">
                {notifications.map(notification => (
                    <Notification 
                        key={notification.id}
                        notification={notification}
                        onClose={() => window.NotificationManager.removeNotification(notification.id)}
                    />
                ))}
            </div>
        );
    } catch (error) {
        console.error('NotificationContainer component error:', error);
        return null;
    }
}

function Notification({ notification, onClose }) {
    try {
        const { type, message, actionText, onAction } = notification;
        
        // Define styles based on notification type
        const styles = {
            success: {
                bg: 'bg-green-50',
                border: 'border-green-400',
                icon: 'text-green-400',
                iconClass: 'fa-check-circle',
                title: 'Success'
            },
            error: {
                bg: 'bg-red-50',
                border: 'border-red-400',
                icon: 'text-red-400',
                iconClass: 'fa-exclamation-circle',
                title: 'Error'
            },
            warning: {
                bg: 'bg-yellow-50',
                border: 'border-yellow-400',
                icon: 'text-yellow-400',
                iconClass: 'fa-exclamation-triangle',
                title: 'Warning'
            },
            info: {
                bg: 'bg-blue-50',
                border: 'border-blue-400',
                icon: 'text-blue-400',
                iconClass: 'fa-info-circle',
                title: 'Information'
            }
        };
        
        const style = styles[type] || styles.info;
        
        return (
            <div className={`${style.bg} border-l-4 ${style.border} p-4 rounded shadow-md transition-all duration-300 ease-in-out transform translate-x-0 opacity-100`}>
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        <i className={`fas ${style.iconClass} ${style.icon}`}></i>
                    </div>
                    <div className="ml-3 w-0 flex-1 pt-0.5">
                        <p className="text-sm font-medium text-gray-900">{style.title}</p>
                        <p className="mt-1 text-sm text-gray-500">{message}</p>
                        {actionText && onAction && (
                            <div className="mt-3">
                                <button
                                    type="button"
                                    onClick={() => {
                                        onAction();
                                        onClose();
                                    }}
                                    className="text-sm font-medium text-indigo-600 hover:text-indigo-500"
                                >
                                    {actionText}
                                </button>
                            </div>
                        )}
                    </div>
                    <div className="ml-4 flex-shrink-0 flex">
                        <button
                            className="inline-flex text-gray-400 hover:text-gray-500"
                            onClick={onClose}
                        >
                            <i className="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Notification component error:', error);
        return null;
    }
}