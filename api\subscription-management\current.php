<?php
require_once '../config/database.php';
require_once '../utils/SecurityUtils.php';
require_once '../utils/AuthUtils.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // Verify authentication
    $authResult = AuthUtils::verifyToken();
    if (!$authResult['success']) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    $user = $authResult['user'];
    $conn = Database::getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetCurrentSubscription($conn, $user);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Current Subscription API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}

function handleGetCurrentSubscription($conn, $user) {
    try {
        // Get user's company
        $companyStmt = $conn->prepare("SELECT object_id FROM companies WHERE owner_id = ? OR id = ?");
        $companyStmt->bind_param("ss", $user['object_id'], $user['company_id']);
        $companyStmt->execute();
        $companyResult = $companyStmt->get_result();
        
        if ($companyRow = $companyResult->fetch_assoc()) {
            $companyId = $companyRow['object_id'];
            
            // Get current active subscription with plan details
            $subscriptionStmt = $conn->prepare("
                SELECT s.*, p.name as plan_name, p.description as plan_description,
                       p.price_monthly, p.price_yearly, p.features, p.max_users, p.max_leads,
                       p.trial_days, p.is_popular
                FROM subscriptions s
                LEFT JOIN plans p ON s.plan_id = p.id
                WHERE s.company_id = ? AND s.status IN ('active', 'trial')
                ORDER BY s.created_at DESC 
                LIMIT 1
            ");
            $subscriptionStmt->bind_param("s", $companyId);
            $subscriptionStmt->execute();
            $subscriptionResult = $subscriptionStmt->get_result();
            
            if ($subscriptionRow = $subscriptionResult->fetch_assoc()) {
                // Calculate next billing date
                $nextBillingDate = null;
                if ($subscriptionRow['status'] === 'active' && $subscriptionRow['billing_cycle']) {
                    $createdDate = new DateTime($subscriptionRow['created_at']);
                    $interval = $subscriptionRow['billing_cycle'] === 'yearly' ? 'P1Y' : 'P1M';
                    $createdDate->add(new DateInterval($interval));
                    $nextBillingDate = $createdDate->format('Y-m-d');
                }
                
                // Determine current price based on billing cycle
                $currentPrice = $subscriptionRow['billing_cycle'] === 'yearly' 
                    ? $subscriptionRow['price_yearly'] 
                    : $subscriptionRow['price_monthly'];
                
                $subscriptionData = [
                    'subscription_id' => $subscriptionRow['object_id'],
                    'plan_id' => $subscriptionRow['plan_id'],
                    'plan_name' => $subscriptionRow['plan_name'],
                    'plan_description' => $subscriptionRow['plan_description'],
                    'status' => $subscriptionRow['status'],
                    'billing_cycle' => $subscriptionRow['billing_cycle'],
                    'amount' => $currentPrice,
                    'price_monthly' => $subscriptionRow['price_monthly'],
                    'price_yearly' => $subscriptionRow['price_yearly'],
                    'features' => $subscriptionRow['features'] ? json_decode($subscriptionRow['features'], true) : [],
                    'max_users' => $subscriptionRow['max_users'],
                    'max_leads' => $subscriptionRow['max_leads'],
                    'trial_end_date' => $subscriptionRow['trial_end_date'],
                    'next_billing_date' => $nextBillingDate,
                    'created_at' => $subscriptionRow['created_at'],
                    'is_trial' => $subscriptionRow['status'] === 'trial',
                    'payment_method' => 'Credit Card' // Default for now
                ];
                
                echo json_encode(['success' => true, 'data' => $subscriptionData]);
            } else {
                // No active subscription found
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'No active subscription found']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Company not found']);
        }
    } catch (Exception $e) {
        error_log("Error getting current subscription: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to get current subscription']);
    }
}
?>
