<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/utils/SecurityUtils.php';

// Load configuration
Config::load();

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['email']) || !isset($input['password'])) {
            throw new Exception('Email and password are required');
        }
        
        $email = $input['email'];
        $password = $input['password'];
        
        // Connect to database
        $db = Config::getDatabase();
        $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);
        
        if ($conn->connect_error) {
            throw new Exception('Database connection failed: ' . $conn->connect_error);
        }
        
        // Find user
        $stmt = $conn->prepare("SELECT id, object_id, name, email, password, password_hash, role, status, company_id FROM users WHERE email = ?");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            echo json_encode([
                'success' => false,
                'message' => 'User not found',
                'debug' => [
                    'email_searched' => $email,
                    'total_users' => $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count']
                ]
            ]);
            exit;
        }
        
        $user = $result->fetch_assoc();
        
        // Check password
        $passwordValid = false;
        $passwordMethod = '';
        
        if (!empty($user['password_hash'])) {
            $passwordValid = password_verify($password, $user['password_hash']);
            $passwordMethod = 'password_hash field';
        } elseif (!empty($user['password'])) {
            // Check if it's a hashed password
            if (password_get_info($user['password'])['algo'] !== null) {
                $passwordValid = password_verify($password, $user['password']);
                $passwordMethod = 'password field (hashed)';
            } else {
                // Plain text comparison (not recommended)
                $passwordValid = ($password === $user['password']);
                $passwordMethod = 'password field (plain text)';
            }
        }
        
        echo json_encode([
            'success' => $passwordValid,
            'message' => $passwordValid ? 'Login successful' : 'Invalid password',
            'debug' => [
                'user_found' => true,
                'user_status' => $user['status'],
                'user_role' => $user['role'],
                'password_method' => $passwordMethod,
                'password_valid' => $passwordValid,
                'has_password_hash' => !empty($user['password_hash']),
                'has_password' => !empty($user['password']),
                'password_hash_info' => !empty($user['password']) ? password_get_info($user['password']) : null,
                'password_hash_length' => !empty($user['password']) ? strlen($user['password']) : 0
            ],
            'user' => [
                'id' => $user['id'],
                'object_id' => $user['object_id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'status' => $user['status'],
                'company_id' => $user['company_id']
            ]
        ]);
        
        $conn->close();
        
    } else {
        // GET request - show form
        echo json_encode([
            'message' => 'Send POST request with email and password to test login',
            'example' => [
                'email' => '<EMAIL>',
                'password' => 'your_password'
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
