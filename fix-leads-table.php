<?php
/**
 * Fix leads table schema to match CRUD handler expectations
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');

require_once __DIR__ . '/api/db-config.php';

echo "=== Fixing Leads Table Schema ===\n\n";

try {
    // 1. Check database connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    echo "✅ Database connected successfully\n";
    
    // 2. Check if leads table exists
    $result = $conn->query("SHOW TABLES LIKE 'leads'");
    if ($result->num_rows > 0) {
        echo "✅ Leads table exists\n";
        
        // 3. Check current table structure
        echo "\n--- Current Table Structure ---\n";
        $result = $conn->query("DESCRIBE leads");
        $existingColumns = [];
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
        }
        
        // 4. Define required columns for CRUD handler
        $requiredColumns = [
            'object_id' => 'VARCHAR(36) PRIMARY KEY',
            'user_id' => 'VARCHAR(36) NOT NULL',
            'company_id' => 'VARCHAR(36)',
            'name' => 'VARCHAR(255) NOT NULL',
            'email' => 'VARCHAR(255)',
            'phone' => 'VARCHAR(50)',
            'company' => 'VARCHAR(255)',
            'source' => 'VARCHAR(100)',
            'status' => 'VARCHAR(50) DEFAULT \'new\'',
            'priority' => 'VARCHAR(20) DEFAULT \'medium\'',
            'value' => 'DECIMAL(10,2) DEFAULT 0.00',
            'notes' => 'TEXT',
            'tags' => 'JSON',
            'custom_fields' => 'JSON',
            'assigned_to' => 'VARCHAR(36)',
            'last_contact_date' => 'DATETIME',
            'next_follow_up' => 'DATETIME',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];
        
        // 5. Check for missing columns and add them
        echo "\n--- Adding Missing Columns ---\n";
        $columnsAdded = 0;
        
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $existingColumns)) {
                echo "Adding missing column: $column\n";
                
                // Skip PRIMARY KEY for ALTER TABLE
                $alterDefinition = str_replace(' PRIMARY KEY', '', $definition);
                
                $sql = "ALTER TABLE leads ADD COLUMN $column $alterDefinition";
                
                if ($conn->query($sql)) {
                    echo "✅ Added column: $column\n";
                    $columnsAdded++;
                } else {
                    echo "❌ Failed to add column $column: " . $conn->error . "\n";
                }
            }
        }
        
        if ($columnsAdded === 0) {
            echo "✅ All required columns already exist\n";
        } else {
            echo "✅ Added $columnsAdded missing columns\n";
        }
        
        // 6. Add indexes if they don't exist
        echo "\n--- Adding Indexes ---\n";
        $indexes = [
            'idx_user_id' => 'user_id',
            'idx_company_id' => 'company_id',
            'idx_status' => 'status',
            'idx_assigned_to' => 'assigned_to',
            'idx_created_at' => 'created_at'
        ];
        
        foreach ($indexes as $indexName => $column) {
            $sql = "CREATE INDEX $indexName ON leads ($column)";
            if ($conn->query($sql)) {
                echo "✅ Added index: $indexName\n";
            } else {
                // Index might already exist, check if it's a duplicate key error
                if (strpos($conn->error, 'Duplicate key name') !== false) {
                    echo "ℹ️  Index $indexName already exists\n";
                } else {
                    echo "⚠️  Could not add index $indexName: " . $conn->error . "\n";
                }
            }
        }
        
    } else {
        echo "❌ Leads table does not exist\n";
        echo "Creating leads table with proper schema...\n";
        
        $createTableSQL = "
        CREATE TABLE leads (
            object_id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) NOT NULL,
            company_id VARCHAR(36),
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(50),
            company VARCHAR(255),
            source VARCHAR(100),
            status VARCHAR(50) DEFAULT 'new',
            priority VARCHAR(20) DEFAULT 'medium',
            value DECIMAL(10,2) DEFAULT 0.00,
            notes TEXT,
            tags JSON,
            custom_fields JSON,
            assigned_to VARCHAR(36),
            last_contact_date DATETIME,
            next_follow_up DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_company_id (company_id),
            INDEX idx_status (status),
            INDEX idx_assigned_to (assigned_to),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createTableSQL)) {
            echo "✅ Leads table created successfully with proper schema\n";
        } else {
            throw new Exception("Failed to create leads table: " . $conn->error);
        }
    }
    
    // 7. Test the table structure
    echo "\n--- Final Table Structure ---\n";
    $result = $conn->query("DESCRIBE leads");
    while ($row = $result->fetch_assoc()) {
        echo "✅ " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
    
    // 8. Test lead creation
    echo "\n--- Testing Lead Creation ---\n";
    
    $testLead = [
        'object_id' => 'test-lead-' . uniqid(),
        'user_id' => 'test-user-123',
        'name' => 'Test Lead',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'company' => 'Test Company',
        'source' => 'api_test',
        'status' => 'new',
        'priority' => 'medium',
        'value' => 1000.00,
        'notes' => 'Test lead for schema validation'
    ];
    
    $sql = "INSERT INTO leads (object_id, user_id, name, email, phone, company, source, status, priority, value, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Failed to prepare statement: " . $conn->error);
    }
    
    $stmt->bind_param("sssssssssds", 
        $testLead['object_id'],
        $testLead['user_id'],
        $testLead['name'],
        $testLead['email'],
        $testLead['phone'],
        $testLead['company'],
        $testLead['source'],
        $testLead['status'],
        $testLead['priority'],
        $testLead['value'],
        $testLead['notes']
    );
    
    if ($stmt->execute()) {
        echo "✅ Test lead created successfully\n";
        echo "Lead ID: " . $testLead['object_id'] . "\n";
        
        // Clean up test data
        $conn->query("DELETE FROM leads WHERE object_id = '" . $testLead['object_id'] . "'");
        echo "✅ Test lead cleaned up\n";
    } else {
        throw new Exception("Failed to create test lead: " . $stmt->error);
    }
    
    echo "\n=== Schema Fix Complete ===\n";
    echo "✅ Leads table is now compatible with CRUD handler\n";
    echo "✅ All required columns are present\n";
    echo "✅ Indexes are in place for performance\n";
    echo "✅ Lead creation tested successfully\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$conn->close();
?>
