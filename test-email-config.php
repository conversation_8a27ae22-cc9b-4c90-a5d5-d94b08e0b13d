<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/utils/EmailUtils.php';

// Set content type to HTML
header('Content-Type: text/html');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #2563eb;
        }
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9fafb;
        }
        .success {
            color: #16a34a;
            font-weight: bold;
        }
        .error {
            color: #dc2626;
            font-weight: bold;
        }
        pre {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid #e5e7eb;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f1f5f9;
        }
        button, input[type="submit"] {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover, input[type="submit"]:hover {
            background-color: #1d4ed8;
        }
        input[type="email"] {
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>Email Configuration Test</h1>
    <p>This page helps diagnose email configuration issues in your PHP environment.</p>
    
    <div class="card">
        <h2>PHP Mail Configuration</h2>
        <?php
        $mailConfig = EmailUtils::getMailConfigStatus();
        ?>
        <table>
            <tr>
                <th>Setting</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>mail() function available</td>
                <td><?php echo $mailConfig['mail_function_available'] ? '<span class="success">Yes</span>' : '<span class="error">No</span>'; ?></td>
            </tr>
            <tr>
                <td>SMTP Host</td>
                <td><?php echo empty($mailConfig['smtp_host']) ? '<span class="error">Not configured</span>' : $mailConfig['smtp_host']; ?></td>
            </tr>
            <tr>
                <td>SMTP Port</td>
                <td><?php echo $mailConfig['smtp_port']; ?></td>
            </tr>
            <tr>
                <td>Sendmail Path</td>
                <td><?php echo empty($mailConfig['sendmail_path']) ? '<span class="error">Not configured</span>' : $mailConfig['sendmail_path']; ?></td>
            </tr>
            <tr>
                <td>PHP Version</td>
                <td><?php echo $mailConfig['php_version']; ?></td>
            </tr>
        </table>
        
        <?php
        // Check if mail configuration is valid
        $mailValidation = EmailUtils::validateEmailConfig();
        if ($mailValidation['valid']) {
            echo '<p class="success">Mail configuration appears to be valid.</p>';
        } else {
            echo '<p class="error">Mail configuration issue: ' . $mailValidation['error'] . '</p>';
            
            if (isset($mailValidation['details'])) {
                echo '<pre>' . print_r($mailValidation['details'], true) . '</pre>';
            }
            
            echo '<div class="card">';
            echo '<h3>How to Fix</h3>';
            echo '<p>To fix the mail configuration issue, you need to configure your PHP mail settings:</p>';
            echo '<ol>';
            echo '<li>Locate your php.ini file (typically in your PHP installation directory)</li>';
            echo '<li>Set the following values:
                <pre>
[mail function]
; For Win32 only.
SMTP = your-smtp-server.com
smtp_port = 25

; For Win32 only.
sendmail_from = <EMAIL>

; For Unix only.  You may supply arguments as well (default: "sendmail -t -i").
;sendmail_path =
                </pre>
            </li>';
            echo '<li>Replace "your-smtp-server.com" with your actual SMTP server address</li>';
            echo '<li>Replace "<EMAIL>" with your actual email address</li>';
            echo '<li>Restart your web server after making these changes</li>';
            echo '</ol>';
            
            echo '<p>Alternatively, you can use a third-party library like PHPMailer or SwiftMailer to send emails without relying on the PHP mail() function.</p>';
            echo '</div>';
        }
        ?>
    </div>
    
    <div class="card">
        <h2>Send Test Email</h2>
        <form method="post" action="">
            <p>
                <label for="test_email">Email address to send test to:</label><br>
                <input type="email" id="test_email" name="test_email" required placeholder="<EMAIL>">
            </p>
            <p>
                <input type="submit" name="send_test" value="Send Test Email">
            </p>
        </form>
        
        <?php
        // Handle test email sending
        if (isset($_POST['send_test']) && isset($_POST['test_email'])) {
            $testEmail = filter_var($_POST['test_email'], FILTER_SANITIZE_EMAIL);
            
            if (filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                $result = EmailUtils::testEmail($testEmail);
                
                if ($result['success']) {
                    echo '<p class="success">Test email sent successfully to ' . htmlspecialchars($testEmail) . '!</p>';
                    echo '<p>Please check your inbox (and spam folder) to confirm receipt.</p>';
                } else {
                    echo '<p class="error">Failed to send test email: ' . $result['message'] . '</p>';
                    
                    if (isset($result['debug_info'])) {
                        echo '<h3>Debug Information:</h3>';
                        echo '<pre>' . print_r($result['debug_info'], true) . '</pre>';
                    }
                }
            } else {
                echo '<p class="error">Invalid email address provided.</p>';
            }
        }
        ?>
    </div>
    
    <div class="card">
        <h2>Recommendations</h2>
        <p>If you're having trouble with the PHP mail() function, consider these alternatives:</p>
        
        <h3>1. Configure a local mail server</h3>
        <p>For Windows, you can use:</p>
        <ul>
            <li><a href="https://www.hmailserver.com/" target="_blank">hMailServer</a> - Free email server for Windows</li>
            <li><a href="https://www.mercury-mail.com/" target="_blank">Mercury Mail Transport System</a> - Free mail server</li>
        </ul>
        
        <h3>2. Use a third-party library</h3>
        <p>Replace the native mail() function with a more robust library:</p>
        <ul>
            <li><a href="https://github.com/PHPMailer/PHPMailer" target="_blank">PHPMailer</a> - The most popular library for sending emails</li>
            <li><a href="https://symfony.com/doc/current/mailer.html" target="_blank">Symfony Mailer</a> - Modern email sending library</li>
        </ul>
        
        <h3>3. Use an SMTP service</h3>
        <p>Connect to an external SMTP service:</p>
        <ul>
            <li><a href="https://sendgrid.com/" target="_blank">SendGrid</a> - Email delivery service</li>
            <li><a href="https://www.mailgun.com/" target="_blank">Mailgun</a> - Email API service</li>
            <li><a href="https://aws.amazon.com/ses/" target="_blank">Amazon SES</a> - Amazon Simple Email Service</li>
        </ul>
    </div>
    
    <p><a href="index.html">Back to Home</a></p>
</body>
</html>