<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auth Endpoint - Bizma</title>
    <script src="config.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Endpoint Test</h1>
        <p>Test the authentication endpoints directly</p>
        
        <div class="test-section">
            <h2>Test Login Endpoint</h2>
            <div>
                <label>Email:</label>
                <input type="email" id="email" value="<EMAIL>" placeholder="Enter email">
            </div>
            <div>
                <label>Password:</label>
                <input type="password" id="password" value="admin123" placeholder="Enter password">
            </div>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testInvalidLogin()">Test Invalid Login</button>
            <button onclick="testEndpointAvailability()">Test Endpoint Availability</button>
        </div>
        
        <div id="testResult"></div>
    </div>

    <script>
        async function testEndpointAvailability() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div class="info">Testing endpoint availability...</div>';
            
            try {
                // Test if the endpoint exists
                const response = await fetch('/biz/api/simple-auth.php', {
                    method: 'GET'
                });
                
                console.log('GET Response status:', response.status);
                console.log('GET Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('GET Response text:', responseText);
                
                resultDiv.innerHTML = `
                    <div class="info">
                        <h3>Endpoint Availability Test</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Status Text:</strong> ${response.statusText}</p>
                        <p><strong>Response:</strong></p>
                        <pre>${responseText}</pre>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Endpoint Test Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('testResult');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                resultDiv.innerHTML = '<div class="error">Please enter email and password</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Testing login...</div>';
            
            try {
                console.log('Testing login with:', { email, password: '***' });
                
                const response = await fetch('/biz/api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: email,
                        password: password,
                        remember_me: false
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error('Invalid JSON response: ' + responseText);
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Successful</h3>
                            <p><strong>User:</strong> ${data.user.name}</p>
                            <p><strong>Email:</strong> ${data.user.email}</p>
                            <p><strong>Role:</strong> ${data.user.role}</p>
                            <p><strong>Token:</strong> ${data.token.substring(0, 20)}...</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Login test error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testInvalidLogin() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div class="info">Testing invalid login...</div>';
            
            try {
                const response = await fetch('/biz/api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'wrongpassword',
                        remember_me: false
                    })
                });
                
                const responseText = await response.text();
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error('Invalid JSON response: ' + responseText);
                }
                
                if (!data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Invalid Login Properly Rejected</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Security Issue: Invalid Login Accepted</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Auto-test endpoint availability on load
        window.addEventListener('load', () => {
            setTimeout(testEndpointAvailability, 1000);
        });
    </script>
</body>
</html>
