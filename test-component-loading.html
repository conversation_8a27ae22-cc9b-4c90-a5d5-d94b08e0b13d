<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Component Loading - Bizma</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Component Loading Test</h1>
        <p>Test if component files are loading correctly</p>
        
        <button onclick="testComponentFiles()">Test Component Files</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        async function testComponentFiles() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">Testing component files...</div>';
            
            const componentFiles = [
                '/biz/components/common/LoadingSpinner.js',
                '/biz/components/common/ErrorMessage.js',
                '/biz/components/common/NotificationContainer.js',
                '/biz/components/subscriptions/TrialBanner.js',
                '/biz/components/subscriptions/ExtendTrialModal.js',
                '/biz/pages/Dashboard.js',
                '/biz/pages/SuperAdminDashboard.js'
            ];
            
            let results = [];
            
            for (const file of componentFiles) {
                try {
                    const response = await fetch(file);
                    const content = await response.text();
                    
                    if (response.ok) {
                        // Check if it's actually JavaScript
                        if (content.includes('function') || content.includes('const') || content.includes('React')) {
                            results.push({
                                file: file,
                                status: 'success',
                                message: 'JavaScript file loaded correctly',
                                size: content.length
                            });
                        } else if (content.includes('<html>') || content.includes('<!DOCTYPE')) {
                            results.push({
                                file: file,
                                status: 'error',
                                message: 'HTML returned instead of JavaScript',
                                content: content.substring(0, 200) + '...'
                            });
                        } else {
                            results.push({
                                file: file,
                                status: 'warning',
                                message: 'Unexpected content type',
                                content: content.substring(0, 200) + '...'
                            });
                        }
                    } else {
                        results.push({
                            file: file,
                            status: 'error',
                            message: `HTTP ${response.status}: ${response.statusText}`,
                            content: content.substring(0, 200) + '...'
                        });
                    }
                } catch (error) {
                    results.push({
                        file: file,
                        status: 'error',
                        message: `Network error: ${error.message}`
                    });
                }
            }
            
            // Display results
            let html = '<h2>Component Loading Test Results</h2>';
            
            results.forEach(result => {
                const statusClass = result.status === 'success' ? 'success' : 
                                  result.status === 'warning' ? 'info' : 'error';
                
                html += `
                    <div class="test-result ${statusClass}">
                        <h4>${result.file}</h4>
                        <p><strong>Status:</strong> ${result.status.toUpperCase()}</p>
                        <p><strong>Message:</strong> ${result.message}</p>
                        ${result.size ? `<p><strong>Size:</strong> ${result.size} bytes</p>` : ''}
                        ${result.content ? `<p><strong>Content Preview:</strong></p><pre>${result.content}</pre>` : ''}
                    </div>
                `;
            });
            
            // Summary
            const successCount = results.filter(r => r.status === 'success').length;
            const errorCount = results.filter(r => r.status === 'error').length;
            const warningCount = results.filter(r => r.status === 'warning').length;
            
            html += `
                <div class="test-result info">
                    <h4>Summary</h4>
                    <p><strong>Total Files:</strong> ${results.length}</p>
                    <p><strong>Success:</strong> ${successCount}</p>
                    <p><strong>Errors:</strong> ${errorCount}</p>
                    <p><strong>Warnings:</strong> ${warningCount}</p>
                </div>
            `;
            
            resultsDiv.innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testComponentFiles, 1000);
        });
    </script>
</body>
</html>
