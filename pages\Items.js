function Items() {
    try {
        const [view, setView] = React.useState('items'); // 'items', 'categories', 'item-form', 'category-form', 'subcategory-form', 'digital-marketing'
        const [selectedItem, setSelectedItem] = React.useState(null);
        const [selectedCategory, setSelectedCategory] = React.useState(null);
        const [selectedParentCategory, setSelectedParentCategory] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [itemToDelete, setItemToDelete] = React.useState(null);
        const [categoryToDelete, setCategoryToDelete] = React.useState(null);
        const [refreshKey, setRefreshKey] = React.useState(0);

        const handleCreateItem = () => {
            setSelectedItem(null);
            setView('item-form');
        };

        const handleEditItem = (item) => {
            setSelectedItem(item);
            setView('item-form');
        };

        const handleItemClick = (item) => {
            handleEditItem(item);
        };

        const handleCreateCategory = () => {
            setSelectedCategory(null);
            setSelectedParentCategory(null);
            setView('category-form');
        };

        const handleEditCategory = (category) => {
            setSelectedCategory(category);
            setSelectedParentCategory(null);
            setView('category-form');
        };

        const handleCreateSubcategory = (parentCategoryId) => {
            setSelectedCategory(null);
            setSelectedParentCategory(parentCategoryId);
            setView('subcategory-form');
        };

        const handleEditSubcategory = (subcategory, parentCategoryId) => {
            setSelectedCategory(subcategory);
            setSelectedParentCategory(parentCategoryId);
            setView('subcategory-form');
        };

        const handleItemFormSubmit = () => {
            setView('items');
            setSelectedItem(null);
            setNotification({
                type: 'success',
                message: selectedItem ? 'Item updated successfully' : 'Item created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
        };

        const handleCategoryFormSubmit = () => {
            setView(selectedParentCategory ? 'categories' : 'categories');
            setSelectedCategory(null);
            setSelectedParentCategory(null);
            setNotification({
                type: 'success',
                message: selectedCategory 
                    ? (selectedParentCategory ? 'Subcategory updated successfully' : 'Category updated successfully')
                    : (selectedParentCategory ? 'Subcategory created successfully' : 'Category created successfully')
            });
            setTimeout(() => setNotification(null), 3000);
        };

        const handleFormCancel = () => {
            setView('items');
            setSelectedItem(null);
            setSelectedCategory(null);
            setSelectedParentCategory(null);
        };

        const handleDeleteItem = (item) => {
            setItemToDelete(item);
            setCategoryToDelete(null);
            setShowDeleteConfirm(true);
        };

        const handleDeleteCategory = (category, isSubcategory = false, parentCategoryId = null) => {
            setCategoryToDelete({ category, isSubcategory, parentCategoryId });
            setItemToDelete(null);
            setShowDeleteConfirm(true);
        };

        const handleConfirmDelete = async () => {
            try {
                const token = localStorage.getItem('authToken');

                if (itemToDelete) {
                    const response = await fetch(`/api/api.php/item/${itemToDelete.objectId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        setNotification({
                            type: 'success',
                            message: 'Item deleted successfully'
                        });
                    } else {
                        throw new Error('Failed to delete item');
                    }
                } else if (categoryToDelete) {
                    const { category, isSubcategory, parentCategoryId } = categoryToDelete;
                    const objectType = isSubcategory ? `item_subcategory:${parentCategoryId}` : 'item_category';

                    const response = await fetch(`/api/api.php/${objectType}/${category.objectId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        setNotification({
                            type: 'success',
                            message: isSubcategory ? 'Subcategory deleted successfully' : 'Category deleted successfully'
                        });
                    } else {
                        throw new Error('Failed to delete category');
                    }
                }
            } catch (error) {
                console.error('Error deleting:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete item'
                });
            } finally {
                setShowDeleteConfirm(false);
                setItemToDelete(null);
                setCategoryToDelete(null);
                // Force refresh of the items list
                setRefreshKey(prev => prev + 1);
            }
        };

        const handleCancelDelete = () => {
            setShowDeleteConfirm(false);
            setItemToDelete(null);
            setCategoryToDelete(null);
        };

        const renderContent = () => {
            switch (view) {
                case 'items':
                    return <ItemList onItemClick={handleItemClick} key={refreshKey} />;
                case 'categories':
                    return (
                        <CategoryList
                            onCategoryClick={handleEditCategory}
                            onSubcategoryClick={(subcategory, parentCategoryId) => {
                                if (subcategory) {
                                    handleEditSubcategory(subcategory, parentCategoryId);
                                } else {
                                    handleCreateSubcategory(parentCategoryId);
                                }
                            }}
                            key={refreshKey}
                        />
                    );
                case 'item-form':
                    return (
                        <ItemForm
                            item={selectedItem && selectedItem.objectData ? selectedItem.objectData : null}
                            onSubmit={handleItemFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    );
                case 'category-form':
                    return (
                        <CategoryForm
                            category={selectedCategory && selectedCategory.objectData ? selectedCategory.objectData : null}
                            onSubmit={handleCategoryFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    );
                case 'subcategory-form':
                    return (
                        <CategoryForm
                            category={selectedCategory && selectedCategory.objectData ? selectedCategory.objectData : null}
                            isSubcategory={true}
                            parentCategoryId={selectedParentCategory}
                            onSubmit={handleCategoryFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    );
                case 'digital-marketing':
                    return <DigitalMarketingItems />;
                default:
                    return <ItemList onItemClick={handleItemClick} key={refreshKey} />;
            }
        };

        return (
            <div data-name="items-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Products & Services</h1>
                    <div className="flex space-x-4">
                        <Button
                            onClick={() => setView('items')}
                            variant={view === 'items' ? 'primary' : 'secondary'}
                        >
                            Items
                        </Button>
                        <Button
                            onClick={() => setView('categories')}
                            variant={view === 'categories' ? 'primary' : 'secondary'}
                        >
                            Categories
                        </Button>
                        <Button
                            onClick={() => setView('digital-marketing')}
                            variant={view === 'digital-marketing' ? 'primary' : 'secondary'}
                        >
                            Digital Marketing
                        </Button>
                        <Button
                            onClick={handleCreateItem}
                            icon="fas fa-plus"
                        >
                            Add Item
                        </Button>
                        {view === 'categories' && (
                            <Button
                                onClick={handleCreateCategory}
                                icon="fas fa-plus"
                            >
                                Add Category
                            </Button>
                        )}
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                <div className="bg-white rounded-lg shadow p-6">
                    {renderContent()}
                </div>

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <Modal
                        isOpen={showDeleteConfirm}
                        onClose={handleCancelDelete}
                        title={`Delete ${itemToDelete ? 'Item' : (categoryToDelete && categoryToDelete.isSubcategory ? 'Subcategory' : 'Category')}`}
                    >
                        <div className="p-4">
                            <p className="mb-4">
                                Are you sure you want to delete this {itemToDelete ? 'item' : (categoryToDelete && categoryToDelete.isSubcategory ? 'subcategory' : 'category')}?
                                This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={handleCancelDelete}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleConfirmDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Items page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
