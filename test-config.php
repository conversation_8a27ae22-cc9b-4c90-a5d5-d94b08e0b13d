<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    echo json_encode(['step' => 1, 'message' => 'Starting config test']);
    
    // Test config file inclusion
    $configPath = __DIR__ . '/config/config.php';
    if (!file_exists($configPath)) {
        throw new Exception('Config file not found at: ' . $configPath);
    }
    
    require_once $configPath;
    echo json_encode(['step' => 2, 'message' => 'Config file included']);
    
    // Test Config class
    if (!class_exists('Config')) {
        throw new Exception('Config class not found');
    }
    
    echo json_encode(['step' => 3, 'message' => 'Config class exists']);
    
    // Test Config::load()
    Config::load();
    echo json_encode(['step' => 4, 'message' => 'Config loaded']);
    
    // Test Config::getDatabase()
    $dbConfig = Config::getDatabase();
    echo json_encode(['step' => 5, 'message' => 'Database config retrieved', 'db_config' => $dbConfig]);
    
    // Test SecurityUtils
    $securityPath = __DIR__ . '/utils/SecurityUtils.php';
    if (!file_exists($securityPath)) {
        throw new Exception('SecurityUtils file not found at: ' . $securityPath);
    }
    
    require_once $securityPath;
    echo json_encode(['step' => 6, 'message' => 'SecurityUtils included']);
    
    if (!class_exists('SecurityUtils')) {
        throw new Exception('SecurityUtils class not found');
    }
    
    echo json_encode(['step' => 7, 'message' => 'SecurityUtils class exists']);
    
    // Test EmailUtils
    $emailPath = __DIR__ . '/utils/EmailUtils.php';
    if (!file_exists($emailPath)) {
        throw new Exception('EmailUtils file not found at: ' . $emailPath);
    }
    
    require_once $emailPath;
    echo json_encode(['step' => 8, 'message' => 'EmailUtils included']);
    
    echo json_encode(['success' => true, 'message' => 'All dependencies loaded successfully']);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Fatal error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
