<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final API Test - Bizma</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="config.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">🚀 Final API Test - All Systems Check</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Business Types API -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-600">
                    <i class="fas fa-building mr-2"></i>Business Types
                </h2>
                <div id="business-types-result" class="mb-4">
                    <div class="text-gray-600">Ready to test...</div>
                </div>
                <button onclick="testBusinessTypes()" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test Business Types API
                </button>
            </div>

            <!-- Pricing Plans API -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-600">
                    <i class="fas fa-dollar-sign mr-2"></i>Pricing Plans
                </h2>
                <div id="pricing-plans-result" class="mb-4">
                    <div class="text-gray-600">Ready to test...</div>
                </div>
                <button onclick="testPricingPlans()" class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Pricing Plans API
                </button>
            </div>

            <!-- Subscription Management -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-purple-600">
                    <i class="fas fa-credit-card mr-2"></i>Subscriptions
                </h2>
                <div id="subscription-result" class="mb-4">
                    <div class="text-gray-600">Ready to test...</div>
                </div>
                <button onclick="testSubscriptionAPI()" class="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test Subscription API
                </button>
            </div>

            <!-- Lead Management -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-red-600">
                    <i class="fas fa-users mr-2"></i>Lead Management
                </h2>
                <div id="lead-result" class="mb-4">
                    <div class="text-gray-600">Ready to test...</div>
                </div>
                <button onclick="testLeadAPI()" class="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                    Test Lead API
                </button>
            </div>

            <!-- Super Admin APIs -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-600">
                    <i class="fas fa-crown mr-2"></i>Super Admin
                </h2>
                <div id="super-admin-result" class="mb-4">
                    <div class="text-gray-600">Ready to test...</div>
                </div>
                <button onclick="testSuperAdminAPI()" class="w-full bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                    Test Super Admin API
                </button>
            </div>

            <!-- Overall Status -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-600">
                    <i class="fas fa-check-circle mr-2"></i>Overall Status
                </h2>
                <div id="overall-result" class="mb-4">
                    <div class="text-gray-600">Click "Test All" to run comprehensive test</div>
                </div>
                <button onclick="testAll()" class="w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Test All APIs
                </button>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">📊 Detailed Test Results</h2>
            <div id="detailed-results" class="bg-gray-50 rounded p-4 max-h-96 overflow-y-auto font-mono text-sm">
                <div class="text-gray-600">No tests run yet. Click any test button above to start.</div>
            </div>
            <button onclick="clearResults()" class="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                Clear Results
            </button>
        </div>
    </div>

    <script>
        function log(message) {
            const resultsDiv = document.getElementById('detailed-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const icon = status === 'success' ? 'check' : status === 'error' ? 'times' : 'spinner fa-spin';
            const color = status === 'success' ? 'green' : status === 'error' ? 'red' : 'blue';
            
            element.innerHTML = `
                <div class="text-${color}-600">
                    <i class="fas fa-${icon} mr-2"></i>${message}
                </div>
            `;
        }

        async function testBusinessTypes() {
            updateStatus('business-types-result', 'loading', 'Testing...');
            log('Testing Business Types API...');
            
            try {
                const response = await fetch('/biz/api/business-types.php');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateStatus('business-types-result', 'success', `✅ ${data.data.length} business types loaded`);
                    log(`✅ Business Types API: ${data.data.length} types loaded successfully`);
                } else {
                    updateStatus('business-types-result', 'error', `❌ API Error: ${data.message || 'Unknown error'}`);
                    log(`❌ Business Types API Error: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                updateStatus('business-types-result', 'error', `❌ Network Error: ${error.message}`);
                log(`❌ Business Types Network Error: ${error.message}`);
            }
        }

        async function testPricingPlans() {
            updateStatus('pricing-plans-result', 'loading', 'Testing...');
            log('Testing Pricing Plans API...');
            
            try {
                const response = await fetch('/biz/api/pricing-plans.php');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateStatus('pricing-plans-result', 'success', `✅ ${data.data.length} pricing plans loaded`);
                    log(`✅ Pricing Plans API: ${data.data.length} plans loaded successfully`);
                } else {
                    updateStatus('pricing-plans-result', 'error', `❌ API Error: ${data.message || 'Unknown error'}`);
                    log(`❌ Pricing Plans API Error: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                updateStatus('pricing-plans-result', 'error', `❌ Network Error: ${error.message}`);
                log(`❌ Pricing Plans Network Error: ${error.message}`);
            }
        }

        async function testSubscriptionAPI() {
            updateStatus('subscription-result', 'loading', 'Testing...');
            log('Testing Subscription Management API...');
            
            const endpoints = [
                '/biz/api/api.php/subscription-management/current',
                '/biz/api/api.php/subscription-management/trial-status'
            ];
            
            let successCount = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    
                    if (response.status === 401 || response.status === 403) {
                        // Expected for unauthenticated requests
                        successCount++;
                        log(`✅ ${endpoint}: Properly protected (${response.status})`);
                    } else if (response.ok) {
                        successCount++;
                        log(`✅ ${endpoint}: Accessible (${response.status})`);
                    } else {
                        log(`⚠️ ${endpoint}: Unexpected status (${response.status})`);
                    }
                } catch (error) {
                    log(`❌ ${endpoint}: Network error - ${error.message}`);
                }
            }
            
            if (successCount === endpoints.length) {
                updateStatus('subscription-result', 'success', `✅ All ${endpoints.length} endpoints working`);
            } else {
                updateStatus('subscription-result', 'error', `❌ ${successCount}/${endpoints.length} endpoints working`);
            }
        }

        async function testLeadAPI() {
            updateStatus('lead-result', 'loading', 'Testing...');
            log('Testing Lead Management API...');
            
            try {
                const response = await fetch('/biz/api/api.php/lead', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test Lead',
                        email: '<EMAIL>'
                    })
                });
                
                if (response.status === 401 || response.status === 403) {
                    updateStatus('lead-result', 'success', '✅ Properly protected (auth required)');
                    log('✅ Lead API: Properly protected, authentication required');
                } else if (response.ok) {
                    updateStatus('lead-result', 'success', '✅ API accessible');
                    log('✅ Lead API: Accessible and working');
                } else {
                    const errorText = await response.text();
                    updateStatus('lead-result', 'error', `❌ Error: ${response.status}`);
                    log(`❌ Lead API Error: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                updateStatus('lead-result', 'error', `❌ Network Error: ${error.message}`);
                log(`❌ Lead API Network Error: ${error.message}`);
            }
        }

        async function testSuperAdminAPI() {
            updateStatus('super-admin-result', 'loading', 'Testing...');
            log('Testing Super Admin API...');
            
            const endpoints = [
                '/biz/api/api.php/super-admin/dashboard',
                '/biz/api/api.php/super-admin/policy-pages'
            ];
            
            let successCount = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    
                    if (response.status === 401 || response.status === 403) {
                        // Expected for unauthenticated requests
                        successCount++;
                        log(`✅ ${endpoint}: Properly protected (${response.status})`);
                    } else if (response.ok) {
                        successCount++;
                        log(`✅ ${endpoint}: Accessible (${response.status})`);
                    } else {
                        log(`⚠️ ${endpoint}: Unexpected status (${response.status})`);
                    }
                } catch (error) {
                    log(`❌ ${endpoint}: Network error - ${error.message}`);
                }
            }
            
            if (successCount === endpoints.length) {
                updateStatus('super-admin-result', 'success', `✅ All ${endpoints.length} endpoints working`);
            } else {
                updateStatus('super-admin-result', 'error', `❌ ${successCount}/${endpoints.length} endpoints working`);
            }
        }

        async function testAll() {
            updateStatus('overall-result', 'loading', 'Running all tests...');
            log('=== STARTING COMPREHENSIVE API TEST ===');
            
            await testBusinessTypes();
            await testPricingPlans();
            await testSubscriptionAPI();
            await testLeadAPI();
            await testSuperAdminAPI();
            
            updateStatus('overall-result', 'success', '✅ All tests completed!');
            log('=== ALL TESTS COMPLETED ===');
        }

        function clearResults() {
            document.getElementById('detailed-results').innerHTML = '<div class="text-gray-600">Results cleared. Click any test button to start.</div>';
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            log('Page loaded. Ready to test APIs.');
            setTimeout(() => {
                testBusinessTypes();
                testPricingPlans();
            }, 1000);
        });
    </script>
</body>
</html>
