<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test - Bizma</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        input[type="email"] {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 Email Configuration Test</h1>
        <p>This tool helps you test and configure email settings for Bizma.</p>
        
        <div class="config-section">
            <h2>📋 Current Configuration</h2>
            <div id="configStatus">Loading configuration...</div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Email Sending</h2>
            <p>Enter your email address to test if email sending is working:</p>
            <input type="email" id="testEmail" placeholder="<EMAIL>">
            <button onclick="testEmail()">Send Test Email</button>
            <div id="testResult"></div>
        </div>
        
        <div class="config-section">
            <h2>⚙️ Configuration Guide</h2>
            <p>To configure email sending, update your <code>.env</code> file with the following settings:</p>
            <pre>
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME="Your Business Name"
            </pre>
            
            <h3>📝 Gmail Setup Instructions:</h3>
            <ol>
                <li>Enable 2-factor authentication on your Gmail account</li>
                <li>Generate an App Password: <a href="https://myaccount.google.com/apppasswords" target="_blank">Google App Passwords</a></li>
                <li>Use the App Password (not your regular password) in SMTP_PASSWORD</li>
                <li>Update the .env file with your settings</li>
                <li>Test the configuration using this page</li>
            </ol>
        </div>
    </div>

    <script>
        // Load configuration status on page load
        window.addEventListener('load', loadConfigStatus);
        
        async function loadConfigStatus() {
            try {
                const response = await fetch('/biz/test-email.php');
                const data = await response.json();
                
                if (data.config) {
                    displayConfigStatus(data.config);
                } else {
                    document.getElementById('configStatus').innerHTML = 
                        '<div class="error">Failed to load configuration</div>';
                }
            } catch (error) {
                document.getElementById('configStatus').innerHTML = 
                    '<div class="error">Error loading configuration: ' + error.message + '</div>';
            }
        }
        
        function displayConfigStatus(config) {
            const statusHtml = `
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>SMTP Host:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.smtp_host}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>SMTP Port:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.smtp_port}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>SMTP Username:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.smtp_username}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>From Email:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.smtp_from_email}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>From Name:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.smtp_from_name}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>PHP Mail Available:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.php_mail_available ? '✅ Yes' : '❌ No'}</td></tr>
                    <tr><td style="padding: 5px; border-bottom: 1px solid #ddd;"><strong>Current PHP SMTP:</strong></td><td style="padding: 5px; border-bottom: 1px solid #ddd;">${config.current_smtp_ini}</td></tr>
                    <tr><td style="padding: 5px;"><strong>Current PHP Port:</strong></td><td style="padding: 5px;">${config.current_port_ini}</td></tr>
                </table>
            `;
            
            document.getElementById('configStatus').innerHTML = statusHtml;
        }
        
        async function testEmail() {
            const email = document.getElementById('testEmail').value;
            const resultDiv = document.getElementById('testResult');
            
            if (!email) {
                resultDiv.innerHTML = '<div class="error">Please enter an email address</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Sending test email...</div>';
            
            try {
                const response = await fetch('/biz/test-email.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email: email })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.message || data.error}</div>`;
                    if (data.debug_info) {
                        resultDiv.innerHTML += `<pre>${JSON.stringify(data.debug_info, null, 2)}</pre>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
