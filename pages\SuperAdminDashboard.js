function SuperAdminDashboard() {
    const authContext = React.useContext(window.AuthContext || AuthContext);
    const [dashboardData, setDashboardData] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [activeTab, setActiveTab] = React.useState('overview');
    const [notification, setNotification] = React.useState(null);

    React.useEffect(() => {
        console.log('SuperAdminDashboard - Auth context:', authContext);
        console.log('SuperAdminDashboard - User:', authContext?.user);
        console.log('SuperAdminDashboard - User role:', authContext?.user?.role);

        // Check if authContext is available and user is super admin
        if (!authContext || !authContext.user) {
            console.log('SuperAdminDashboard - Auth context or user not available yet');
            return; // Wait for auth context to be available
        }

        if (authContext.user.role !== 'super_admin') {
            console.error('SuperAdminDashboard - Access denied: User is not a super admin');
            console.log('SuperAdminDashboard - User role:', authContext.user.role);
            // Don't redirect here, let app.js handle the redirect
            return;
        }

        console.log('SuperAdminDashboard - User is a super admin, loading dashboard data');
        // User is a super admin, load dashboard data
        loadDashboardData();
    }, [authContext]);

    const loadDashboardData = async () => {
        if (!authContext || !authContext.token) {
            console.error('No auth context or token available');
            return;
        }

        try {
            setLoading(true);
            const response = await fetch(window.getApiUrl('/super-admin/dashboard'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load dashboard data');
            }

            const data = await response.json();
            setDashboardData(data.data);
        } catch (error) {
            console.error('Error loading dashboard:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load dashboard data'
            });
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    // Show loading if auth context is not available yet
    if (!authContext) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading Super Admin Dashboard...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow-sm border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Super Admin Dashboard</h1>
                            <p className="mt-1 text-sm text-gray-500">Bizma SaaS Management Console</p>
                        </div>
                        <div className="flex items-center space-x-4">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Super Admin
                            </span>
                            <span className="text-sm text-gray-500">
                                {authContext.user.name}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation Tabs */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="flex space-x-8">
                        {[
                            { id: 'overview', name: 'Overview', icon: '📊' },
                            { id: 'companies', name: 'Companies', icon: '🏢' },
                            { id: 'users', name: 'Users', icon: '👥' },
                            { id: 'subscriptions', name: 'Subscriptions', icon: '💳' },
                            { id: 'analytics', name: 'Analytics', icon: '📈' },
                            { id: 'cms', name: 'CMS', icon: '📝' },
                            { id: 'settings', name: 'Settings', icon: '⚙️' }
                        ].map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === tab.id
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <span className="mr-2">{tab.icon}</span>
                                {tab.name}
                            </button>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {activeTab === 'overview' && (
                    <OverviewTab dashboardData={dashboardData} formatCurrency={formatCurrency} formatDate={formatDate} />
                )}
                {activeTab === 'companies' && (
                    <CompaniesTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'users' && (
                    <UsersTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'subscriptions' && (
                    <SubscriptionsTab authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'analytics' && (
                    <AnalyticsDashboard authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'cms' && (
                    <PolicyPagesManager authContext={authContext} setNotification={setNotification} />
                )}
                {activeTab === 'settings' && (
                    <SettingsTab authContext={authContext} setNotification={setNotification} />
                )}
            </div>

            {/* Notification */}
            {notification && (
                <Notification
                    type={notification.type}
                    message={notification.message}
                    onClose={() => setNotification(null)}
                />
            )}
        </div>
    );
}

// Overview Tab Component
function OverviewTab({ dashboardData, formatCurrency, formatDate }) {
    if (!dashboardData) return <div>Loading...</div>;

    const { stats, recent_companies, subscription_distribution } = dashboardData;

    return (
        <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">🏢</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Companies</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.total_companies}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div className="bg-gray-50 px-5 py-3">
                        <div className="text-sm">
                            <span className="text-green-600 font-medium">+{stats.new_companies_week}</span>
                            <span className="text-gray-500"> this week</span>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">👥</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.total_users}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <div className="bg-gray-50 px-5 py-3">
                        <div className="text-sm">
                            <span className="text-green-600 font-medium">+{stats.new_users_week}</span>
                            <span className="text-gray-500"> this week</span>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">💳</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Active Subscriptions</dt>
                                    <dd className="text-lg font-medium text-gray-900">{stats.active_subscriptions}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <span className="text-white text-sm">💰</span>
                                </div>
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.monthly_revenue)}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Companies */}
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Companies</h3>
                    <div className="overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {recent_companies.map((company, index) => (
                                    <tr key={index}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {company.company_name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div>
                                                <div className="font-medium">{company.owner_name}</div>
                                                <div className="text-gray-400">{company.owner_email}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {formatDate(company.created_at)}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Companies Tab Component
function CompaniesTab({ authContext, setNotification }) {
    const [companies, setCompanies] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [statusFilter, setStatusFilter] = React.useState('');

    React.useEffect(() => {
        loadCompanies();
    }, [searchTerm, statusFilter]);

    const loadCompanies = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (statusFilter) params.append('status', statusFilter);

            const response = await fetch(`${window.getApiUrl('/super-admin/companies/list')}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load companies');
            }

            const data = await response.json();
            setCompanies(data.data.companies);
        } catch (error) {
            console.error('Error loading companies:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load companies'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleStatusChange = async (companyId, newStatus) => {
        try {
            const action = newStatus === 'suspended' ? 'suspend' : 'activate';
            const response = await fetch(window.getApiUrl(`/super-admin/companies/${action}`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ company_id: companyId })
            });

            if (!response.ok) {
                throw new Error(`Failed to ${action} company`);
            }

            setNotification({
                type: 'success',
                message: `Company ${action}d successfully`
            });

            loadCompanies();
        } catch (error) {
            console.error('Error updating company status:', error);
            setNotification({
                type: 'error',
                message: `Failed to update company status`
            });
        }
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search companies, owners..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Statuses</option>
                            <option value="active">Active</option>
                            <option value="suspended">Suspended</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Companies Table */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Companies Management</h3>

                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-500">Loading companies...</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {companies.map((company) => (
                                        <tr key={company.object_id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{company.name}</div>
                                                    <div className="text-sm text-gray-500">{company.email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{company.owner_name}</div>
                                                    <div className="text-sm text-gray-500">{company.owner_email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {company.user_count}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {company.current_plan || 'Free'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    company.status === 'active' ? 'bg-green-100 text-green-800' :
                                                    company.status === 'suspended' ? 'bg-red-100 text-red-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {company.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex space-x-2">
                                                    {company.status === 'active' ? (
                                                        <button
                                                            onClick={() => handleStatusChange(company.object_id, 'suspended')}
                                                            className="text-red-600 hover:text-red-900"
                                                        >
                                                            Suspend
                                                        </button>
                                                    ) : (
                                                        <button
                                                            onClick={() => handleStatusChange(company.object_id, 'active')}
                                                            className="text-green-600 hover:text-green-900"
                                                        >
                                                            Activate
                                                        </button>
                                                    )}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

// Users Tab Component
function UsersTab({ authContext, setNotification }) {
    const [users, setUsers] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [roleFilter, setRoleFilter] = React.useState('');

    React.useEffect(() => {
        loadUsers();
    }, [searchTerm, roleFilter]);

    const loadUsers = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams();
            if (searchTerm) params.append('search', searchTerm);
            if (roleFilter) params.append('role', roleFilter);

            const response = await fetch(window.getApiUrl(`/super-admin/users/list?${params}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load users');
            }

            const data = await response.json();
            setUsers(data.data.users);
        } catch (error) {
            console.error('Error loading users:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load users'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search users..."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <select
                            value={roleFilter}
                            onChange={(e) => setRoleFilter(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="user">User</option>
                            <option value="viewer">Viewer</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Users Table */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Users Management</h3>

                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-2 text-gray-500">Loading users...</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {users.map((user) => (
                                        <tr key={user.object_id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                                    <div className="text-sm text-gray-500">{user.email}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {user.company_name || 'No Company'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                                                    user.role === 'user' ? 'bg-blue-100 text-blue-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {user.role}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    user.status === 'active' ? 'bg-green-100 text-green-800' :
                                                    user.status === 'suspended' ? 'bg-red-100 text-red-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                    {user.status}
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

// Placeholder components for other tabs
function SubscriptionsTab({ authContext, setNotification }) {
    const [subscriptions, setSubscriptions] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [searchTerm, setSearchTerm] = React.useState('');
    const [statusFilter, setStatusFilter] = React.useState('all');
    const [showExtendTrialModal, setShowExtendTrialModal] = React.useState(false);
    const [selectedSubscription, setSelectedSubscription] = React.useState(null);
    const [daysToAdd, setDaysToAdd] = React.useState(7);
    const [processing, setProcessing] = React.useState(false);

    React.useEffect(() => {
        fetchSubscriptions();
    }, []);

    const fetchSubscriptions = async () => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/subscriptions/list'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                setSubscriptions(data.data.subscriptions || []);
            }
        } catch (error) {
            console.error('Error fetching subscriptions:', error);
            setNotification({ type: 'error', message: 'Failed to load subscriptions' });
        } finally {
            setLoading(false);
        }
    };
    
    const extendTrial = async () => {
        if (!selectedSubscription || !daysToAdd) return;
        
        setProcessing(true);
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/subscriptions/${selectedSubscription.id}/extend-trial`), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ daysToAdd })
            });
            
            const data = await response.json();
            
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: `Trial extended by ${daysToAdd} days successfully`
                });
                
                // Close modal and refresh subscriptions
                setShowExtendTrialModal(false);
                fetchSubscriptions();
            } else {
                throw new Error(data.message || 'Failed to extend trial');
            }
        } catch (error) {
            console.error('Error extending trial:', error);
            setNotification({
                type: 'error',
                message: 'Failed to extend trial: ' + (error.message || 'Unknown error')
            });
        } finally {
            setProcessing(false);
        }
    };

    const filteredSubscriptions = subscriptions.filter(sub => {
        const matchesSearch = (sub.company_name && sub.company_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
                            (sub.plan_id && sub.plan_id.toLowerCase().includes(searchTerm.toLowerCase()));
        const matchesStatus = statusFilter === 'all' || sub.status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading subscriptions...</div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg p-6">
            {/* Render ExtendTrialModal if showExtendTrialModal is true */}
            {showExtendTrialModal && selectedSubscription && (
                <ExtendTrialModal
                    subscription={selectedSubscription}
                    daysToAdd={daysToAdd}
                    setDaysToAdd={setDaysToAdd}
                    onCancel={() => setShowExtendTrialModal(false)}
                    onConfirm={extendTrial}
                    processing={processing}
                />
            )}
            
            <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">Subscription Management</h3>
            </div>

            <div className="flex gap-4 mb-6">
                <input
                    type="text"
                    placeholder="Search subscriptions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="expired">Expired</option>
                </select>
            </div>

            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Billing</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {filteredSubscriptions.map((subscription) => (
                            <tr key={subscription.id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{subscription.company_name}</div>
                                    <div className="text-sm text-gray-500">{subscription.company_email}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">{subscription.plan_id}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                                        subscription.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                    }`}>
                                        {subscription.status}
                                    </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${subscription.amount}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {subscription.next_billing_date || 'N/A'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button 
                                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                                        onClick={() => {
                                            // View subscription details
                                            console.log('View subscription', subscription);
                                        }}
                                    >
                                        View
                                    </button>
                                    {subscription.plan_id === 'trial' && (
                                        <button 
                                            className="text-green-600 hover:text-green-900 mr-3"
                                            onClick={() => {
                                                setSelectedSubscription(subscription);
                                                setShowExtendTrialModal(true);
                                            }}
                                        >
                                            Extend Trial
                                        </button>
                                    )}
                                    <button 
                                        className="text-red-600 hover:text-red-900"
                                        onClick={() => {
                                            // Cancel subscription
                                            console.log('Cancel subscription', subscription);
                                        }}
                                    >
                                        Cancel
                                    </button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                {filteredSubscriptions.length === 0 && (
                    <div className="text-center py-8 text-gray-500">No subscriptions found</div>
                )}
            </div>
        </div>
    );
}

function AnalyticsTab({ authContext, setNotification }) {
    const [analytics, setAnalytics] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [timeRange, setTimeRange] = React.useState('30');

    React.useEffect(() => {
        fetchAnalytics();
    }, [timeRange]);

    const fetchAnalytics = async () => {
        try {
            const response = await fetch(window.getApiUrl(`/super-admin/analytics?days=${timeRange}`), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success) {
                setAnalytics(data.data);
            }
        } catch (error) {
            console.error('Error fetching analytics:', error);
            setNotification({ type: 'error', message: 'Failed to load analytics' });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading analytics...</div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-lg font-medium text-gray-900">Analytics & Reports</h3>
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>

                {analytics && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">$</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                                    <p className="text-2xl font-semibold text-gray-900">${analytics.revenue && analytics.revenue.total ? analytics.revenue.total : '0'}</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">↗</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Growth Rate</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.growth && analytics.growth.rate ? analytics.growth.rate : '0'}%</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">👥</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">New Customers</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.customers && analytics.customers.new ? analytics.customers.new : '0'}</p>
                                </div>
                            </div>
                        </div>

                        <div className="bg-orange-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">%</span>
                                    </div>
                                </div>
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-500">Churn Rate</p>
                                    <p className="text-2xl font-semibold text-gray-900">{analytics.churn && analytics.churn.rate ? analytics.churn.rate : '0'}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-md font-medium text-gray-900 mb-4">Top Performing Plans</h4>
                        {analytics && analytics.top_plans && analytics.top_plans.map((plan, index) => (
                            <div key={index} className="flex justify-between items-center py-2">
                                <span className="text-sm text-gray-600">{plan.name}</span>
                                <span className="text-sm font-medium text-gray-900">{plan.count} subscribers</span>
                            </div>
                        )) || <p className="text-gray-500">No data available</p>}
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-md font-medium text-gray-900 mb-4">Recent Activity</h4>
                        {analytics && analytics.recent_activity && analytics.recent_activity.map((activity, index) => (
                            <div key={index} className="flex justify-between items-center py-2">
                                <span className="text-sm text-gray-600">{activity.description}</span>
                                <span className="text-xs text-gray-500">{activity.time}</span>
                            </div>
                        )) || <p className="text-gray-500">No recent activity</p>}
                    </div>
                </div>
            </div>
        </div>
    );
}

function SettingsTab({ authContext, setNotification }) {
    const [settings, setSettings] = React.useState({
        site_name: 'Bizma',
        site_description: 'Business Management SaaS Platform',
        maintenance_mode: false,
        registration_enabled: true,
        email_verification_required: true,
        max_companies_per_user: 5,
        default_trial_days: 14,
        support_email: '<EMAIL>'
    });
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);

    React.useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            const response = await fetch(window.getApiUrl('/super-admin/settings'), {
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (data.success && data.data.settings) {
                // Convert string boolean values to actual booleans
                const normalizedSettings = { ...data.data.settings };

                // Convert string booleans to actual booleans
                const booleanFields = ['maintenance_mode', 'registration_enabled', 'email_verification_required'];
                booleanFields.forEach(field => {
                    if (normalizedSettings[field] !== undefined) {
                        if (typeof normalizedSettings[field] === 'string') {
                            normalizedSettings[field] = normalizedSettings[field] === 'true' || normalizedSettings[field] === '1';
                        } else {
                            normalizedSettings[field] = Boolean(normalizedSettings[field]);
                        }
                    }
                });

                setSettings({ ...settings, ...normalizedSettings });
            }
        } catch (error) {
            console.error('Error fetching settings:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            const response = await fetch(window.getApiUrl('/super-admin/settings'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ settings })
            });
            const data = await response.json();
            if (data.success) {
                setNotification({ type: 'success', message: 'Settings saved successfully' });
            } else {
                setNotification({ type: 'error', message: data.message || 'Failed to save settings' });
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            setNotification({ type: 'error', message: 'Failed to save settings' });
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (key, value) => {
        setSettings(prev => ({ ...prev, [key]: value }));
    };

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center">Loading settings...</div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">System Settings</h3>
                <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                    {saving ? 'Saving...' : 'Save Changes'}
                </button>
            </div>

            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                        <input
                            type="text"
                            value={settings.site_name}
                            onChange={(e) => handleInputChange('site_name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                        <input
                            type="email"
                            value={settings.support_email}
                            onChange={(e) => handleInputChange('support_email', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                    <textarea
                        value={settings.site_description}
                        onChange={(e) => handleInputChange('site_description', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Max Companies per User</label>
                        <input
                            type="number"
                            value={settings.max_companies_per_user}
                            onChange={(e) => handleInputChange('max_companies_per_user', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Default Trial Days</label>
                        <input
                            type="number"
                            value={settings.default_trial_days}
                            onChange={(e) => handleInputChange('default_trial_days', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                <div className="space-y-4">
                    <h4 className="text-md font-medium text-gray-900">System Controls</h4>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Maintenance Mode</label>
                            <p className="text-sm text-gray-500">Temporarily disable access to the application</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.maintenance_mode)}
                                onChange={(e) => handleInputChange('maintenance_mode', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Registration Enabled</label>
                            <p className="text-sm text-gray-500">Allow new users to register</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.registration_enabled)}
                                onChange={(e) => handleInputChange('registration_enabled', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <label className="text-sm font-medium text-gray-700">Email Verification Required</label>
                            <p className="text-sm text-gray-500">Require email verification for new accounts</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                            <input
                                type="checkbox"
                                checked={Boolean(settings.email_verification_required)}
                                onChange={(e) => handleInputChange('email_verification_required', e.target.checked)}
                                className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    );
}

// Enhanced CMS Tab Component with Plan Management
function CMSTab({ authContext, setNotification }) {
    const [activeSection, setActiveSection] = React.useState('plans');
    const [plans, setPlans] = React.useState([]);
    const [businessTypes, setBusinessTypes] = React.useState([]);
    const [systemSettings, setSystemSettings] = React.useState({});
    const [policyPages, setPolicyPages] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);
    const [showPlanModal, setShowPlanModal] = React.useState(false);
    const [editingPlan, setEditingPlan] = React.useState(null);
    const [showPolicyModal, setShowPolicyModal] = React.useState(false);
    const [editingPolicy, setEditingPolicy] = React.useState(null);
    
    // Load CMS data when component mounts
    React.useEffect(() => {
        loadCMSData();
    }, []);

    const loadCMSData = async () => {
        try {
            setLoading(true);

            // Load all CMS data in parallel
            const [plansRes, businessTypesRes, settingsRes, policiesRes] = await Promise.all([
                fetch(window.getApiUrl('/super-admin/plans'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/business-types'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/system-settings'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                }),
                fetch(window.getApiUrl('/super-admin/policy-pages'), {
                    headers: { 'Authorization': `Bearer ${authContext.token}` }
                })
            ]);

            // Process plans data
            if (plansRes.ok) {
                const plansData = await plansRes.json();
                if (plansData.success) {
                    setPlans(plansData.data || []);
                }
            }

            // Process business types data
            if (businessTypesRes.ok) {
                const businessTypesData = await businessTypesRes.json();
                if (businessTypesData.success) {
                    setBusinessTypes(businessTypesData.data || []);
                }
            }

            // Process system settings data
            if (settingsRes.ok) {
                const settingsData = await settingsRes.json();
                if (settingsData.success) {
                    setSystemSettings(settingsData.data || {});
                }
            }

            // Process policy pages data
            if (policiesRes.ok) {
                const policiesData = await policiesRes.json();
                if (policiesData.success) {
                    setPolicyPages(policiesData.data || []);
                }
            }

        } catch (error) {
            console.error('Error loading CMS data:', error);
            setNotification({
                type: 'error',
                message: 'Failed to load CMS data'
            });
        } finally {
            setLoading(false);
        }
    };

    // Plan management functions
    const handleCreatePlan = () => {
        setEditingPlan({
            id: '',
            name: '',
            description: '',
            short_description: '',
            price_monthly: 0,
            price_yearly: 0,
            trial_days: 14,
            features: [],
            limits_data: {},
            business_types: [],
            is_trial_available: true,
            is_visible: true,
            is_popular: false,
            sort_order: 0
        });
        setShowPlanModal(true);
    };

    const handleEditPlan = (plan) => {
        setEditingPlan({
            ...plan,
            features: Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]'),
            limits_data: typeof plan.limits_data === 'object' ? plan.limits_data : JSON.parse(plan.limits_data || '{}'),
            business_types: Array.isArray(plan.business_types) ? plan.business_types : JSON.parse(plan.business_types || '[]')
        });
        setShowPlanModal(true);
    };

    const handleSavePlan = async (planData) => {
        setSaving(true);
        try {
            const url = planData.id ?
                window.getApiUrl(`/super-admin/plans/${planData.id}`) :
                window.getApiUrl('/super-admin/plans');

            const method = planData.id ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    section,
                    content: content[section]
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: `${section.charAt(0).toUpperCase() + section.slice(1)} content updated successfully`
                });
            } else {
                throw new Error(data.message || 'Failed to update content');
            }
        } catch (error) {
            console.error('Error saving content:', error);
            setNotification({
                type: 'error',
                message: 'Failed to update content: ' + (error.message || 'Unknown error')
            });
        } finally {
            setSaving(false);
        }
    };

    const handleDeletePlan = async (planId) => {
        if (!confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(window.getApiUrl(`/super-admin/plans/${planId}`), {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${authContext.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            if (data.success) {
                setNotification({
                    type: 'success',
                    message: 'Plan deleted successfully'
                });
                loadCMSData(); // Reload data
            } else {
                setNotification({
                    type: 'error',
                    message: data.message || 'Failed to delete plan'
                });
            }
        } catch (error) {
            console.error('Error deleting plan:', error);
            setNotification({
                type: 'error',
                message: 'Failed to delete plan'
            });
        }
    };

    const renderPlansManagement = () => (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Pricing Plans Management</h3>
                <button
                    onClick={handleCreatePlan}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                    <i className="fas fa-plus mr-2"></i>
                    Create New Plan
                </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {plans.map((plan) => (
                    <div key={plan.id} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                        <div className="flex justify-between items-start mb-4">
                            <div>
                                <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                                <p className="text-sm text-gray-600">{plan.short_description}</p>
                            </div>
                            <div className="flex space-x-2">
                                {plan.is_popular && (
                                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        Popular
                                    </span>
                                )}
                                {!plan.is_visible && (
                                    <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                        Hidden
                                    </span>
                                )}
                            </div>
                        </div>

                        <div className="mb-4">
                            <div className="flex items-baseline">
                                <span className="text-2xl font-bold text-gray-900">₹{plan.price_monthly}</span>
                                <span className="text-gray-600 ml-1">/month</span>
                            </div>
                            <div className="text-sm text-gray-600">
                                ₹{plan.price_yearly}/year (Save ₹{(plan.price_monthly * 12) - plan.price_yearly})
                            </div>
                        </div>

                        <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-2">Features:</p>
                            <div className="space-y-1">
                                {(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).slice(0, 3).map((feature, index) => (
                                    <div key={index} className="flex items-center text-sm text-gray-600">
                                        <i className="fas fa-check text-green-500 mr-2 text-xs"></i>
                                        {feature}
                                    </div>
                                ))}
                                {(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).length > 3 && (
                                    <div className="text-xs text-gray-500">
                                        +{(Array.isArray(plan.features) ? plan.features : JSON.parse(plan.features || '[]')).length - 3} more features
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="flex space-x-2">
                            <button
                                onClick={() => handleEditPlan(plan)}
                                className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md hover:bg-blue-100 text-sm"
                            >
                                <i className="fas fa-edit mr-1"></i>
                                Edit
                            </button>
                            <button
                                onClick={() => handleDeletePlan(plan.id)}
                                className="flex-1 bg-red-50 text-red-600 px-3 py-2 rounded-md hover:bg-red-100 text-sm"
                            >
                                <i className="fas fa-trash mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {plans.length === 0 && (
                <div className="text-center py-12">
                    <i className="fas fa-credit-card text-gray-300 text-4xl mb-4"></i>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No plans found</h3>
                    <p className="text-gray-600 mb-4">Create your first pricing plan to get started.</p>
                    <button
                        onClick={handleCreatePlan}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Create Plan
                    </button>
                </div>
            )}
        </div>
    );

    const renderContactContent = () => (
        <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Support Email
                    </label>
                    <input
                        type="email"
                        value={content.contact.email}
                        onChange={(e) => handleContentChange('contact', 'email', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number
                    </label>
                    <input
                        type="text"
                        value={content.contact.phone}
                        onChange={(e) => handleContentChange('contact', 'phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Address
                </label>
                <textarea
                    rows={4}
                    value={content.contact.address}
                    onChange={(e) => handleContentChange('contact', 'address', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Hours
                </label>
                <textarea
                    rows={4}
                    value={content.contact.business_hours}
                    onChange={(e) => handleContentChange('contact', 'business_hours', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
            </div>

            <button
                onClick={() => saveContent('contact')}
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
                {saving ? 'Saving...' : 'Save Contact Information'}
            </button>
        </div>
    );

    return (
        <div className="space-y-6">
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-6">Content Management System</h2>

                    {/* Section Navigation */}
                    <div className="border-b border-gray-200 mb-6">
                        <nav className="-mb-px flex space-x-8">
                            {[
                                { id: 'plans', name: 'Pricing Plans', icon: '💰' },
                                { id: 'business-types', name: 'Business Types', icon: '🏢' },
                                { id: 'system-settings', name: 'System Settings', icon: '⚙️' },
                                { id: 'policies', name: 'Policy Pages', icon: '📄' }
                            ].map((section) => (
                                <button
                                    key={section.id}
                                    onClick={() => setActiveSection(section.id)}
                                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                        activeSection === section.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                                >
                                    <span className="mr-2">{section.icon}</span>
                                    {section.name}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Content Sections */}
                    {activeSection === 'plans' && renderPlansManagement()}
                    {activeSection === 'business-types' && (
                        <div className="text-center py-12">
                            <div className="text-gray-400 mb-4">
                                <i className="fas fa-building text-4xl"></i>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Business Types Management</h3>
                            <p className="text-gray-600 mb-4">
                                Manage business types and their default configurations.
                            </p>
                            <p className="text-sm text-gray-500">
                                {businessTypes.length} business types configured
                            </p>
                        </div>
                    )}
                    {activeSection === 'system-settings' && (
                        <SystemSettingsManager
                            authContext={authContext}
                            setNotification={setNotification}
                        />
                    )}
                    {activeSection === 'policies' && (
                        <div className="text-center py-12">
                            <div className="text-gray-400 mb-4">
                                <i className="fas fa-file-alt text-4xl"></i>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Policy Pages Management</h3>
                            <p className="text-gray-600 mb-4">
                                Manage Terms & Conditions, Privacy Policy, and Refund Policy pages.
                            </p>
                            <p className="text-sm text-gray-500">
                                {policyPages.length} policy pages configured
                            </p>
                        </div>
                    )}
                </div>
            </div>

            {/* Plan Modal */}
            {showPlanModal && (
                <PlanModal
                    isOpen={showPlanModal}
                    onClose={() => {
                        setShowPlanModal(false);
                        setEditingPlan(null);
                    }}
                    plan={editingPlan}
                    onSave={handleSavePlan}
                    businessTypes={businessTypes}
                />
            )}
        </div>
    );
}

// Make component globally available
window.SuperAdminDashboard = SuperAdminDashboard;
