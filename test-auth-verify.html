<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auth Verification - Bizma</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Auth Verification Test</h1>
        <p>Test authentication token verification endpoints</p>
        
        <div class="test-section">
            <h2>Current Token Status</h2>
            <div id="tokenStatus">Loading...</div>
        </div>
        
        <div class="test-section">
            <h2>Test Endpoints</h2>
            <button onclick="testMainAuthVerify()">Test Main API Auth Verify</button>
            <button onclick="testSimpleVerify()">Test Simple Verify</button>
            <button onclick="testLogin()">Test Login First</button>
            <div id="testResult"></div>
        </div>
    </div>

    <script>
        // Load config
        const isXamppSetup = window.location.pathname.includes('/biz');
        const API_BASE_URL = isXamppSetup ? '/biz/api/api.php' : '/api/api.php';
        
        window.getApiUrl = function(endpoint) {
            return endpoint.startsWith('/') ? API_BASE_URL + endpoint : API_BASE_URL + '/' + endpoint;
        };
        
        // Check current token status
        window.addEventListener('load', () => {
            const token = localStorage.getItem('authToken');
            const statusDiv = document.getElementById('tokenStatus');
            
            if (token) {
                statusDiv.innerHTML = `
                    <div class="info">
                        <strong>Token Found:</strong> ${token.substring(0, 20)}...
                        <br><strong>Length:</strong> ${token.length} characters
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="error">
                        <strong>No Token Found</strong> - Please login first
                    </div>
                `;
            }
        });
        
        async function testMainAuthVerify() {
            const resultDiv = document.getElementById('testResult');
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No token found. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Testing main API auth verify...</div>';
            
            try {
                const url = window.getApiUrl('/auth/verify');
                console.log('Testing URL:', url);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const responseText = await response.text();
                console.log('Response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    throw new Error('Invalid JSON response: ' + responseText);
                }
                
                if (data.success || response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Main API Auth Verify Success</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Main API Auth Verify Failed</h3>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testSimpleVerify() {
            const resultDiv = document.getElementById('testResult');
            const token = localStorage.getItem('authToken');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">No token found. Please login first.</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Testing simple verify...</div>';
            
            try {
                const response = await fetch('/biz/api/verify-token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Simple Verify Success</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Simple Verify Failed</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div class="info">Testing login...</div>';
            
            try {
                const response = await fetch('/biz/api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: '<EMAIL>',
                        password: 'admin123',
                        remember_me: false
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    localStorage.setItem('authToken', data.token);
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Login Success</h3>
                            <p>Token stored. You can now test verification.</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    
                    // Update token status
                    document.getElementById('tokenStatus').innerHTML = `
                        <div class="info">
                            <strong>Token Found:</strong> ${data.token.substring(0, 20)}...
                            <br><strong>Length:</strong> ${data.token.length} characters
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Login Failed</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
