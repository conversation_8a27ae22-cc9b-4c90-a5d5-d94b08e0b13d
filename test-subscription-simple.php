<?php
/**
 * Simple test for subscription API without authentication
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');

require_once __DIR__ . '/api/db-config.php';

echo "=== Simple Subscription API Test ===\n\n";

try {
    // 1. Test database connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    echo "✅ Database connected\n";
    
    // 2. Check if we have any users
    $result = $conn->query("SELECT object_id, email FROM users LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $testUser = $result->fetch_assoc();
        echo "✅ Found test user: " . $testUser['email'] . "\n";
        
        // 3. Check if subscriptions table exists
        $result = $conn->query("SHOW TABLES LIKE 'subscriptions'");
        if ($result->num_rows > 0) {
            echo "✅ Subscriptions table exists\n";
            
            // 4. Check if user has a subscription
            $stmt = $conn->prepare("SELECT * FROM subscriptions WHERE user_id = ?");
            $stmt->bind_param("s", $testUser['object_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $subscription = $result->fetch_assoc();
                echo "✅ User has subscription: " . $subscription['plan_id'] . " (" . $subscription['status'] . ")\n";
            } else {
                echo "ℹ️  User has no subscription - creating test subscription\n";
                
                // Create a test subscription
                $subscriptionId = 'sub_' . uniqid();
                $stmt = $conn->prepare("
                    INSERT INTO subscriptions (object_id, user_id, plan_id, status, billing_cycle, amount, trial_start_date, trial_end_date) 
                    VALUES (?, ?, 'trial', 'trial', 'monthly', 0.00, NOW(), DATE_ADD(NOW(), INTERVAL 14 DAY))
                ");
                $stmt->bind_param("ss", $subscriptionId, $testUser['object_id']);
                
                if ($stmt->execute()) {
                    echo "✅ Test subscription created\n";
                } else {
                    echo "❌ Failed to create test subscription: " . $stmt->error . "\n";
                }
            }
            
        } else {
            echo "❌ Subscriptions table does not exist\n";
        }
        
    } else {
        echo "❌ No users found in database\n";
    }
    
    // 5. Test the subscription API directly
    echo "\n--- Testing Subscription API Functions ---\n";
    
    // Include the subscription management file
    ob_start();
    
    // Mock the request
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/biz/api/api.php/subscription-management/current';
    
    // Mock a user (bypass authentication for testing)
    $mockUser = [
        'object_id' => $testUser['object_id'],
        'email' => $testUser['email']
    ];
    
    // Test getCurrentSubscription function directly
    if (function_exists('getCurrentSubscription')) {
        echo "Testing getCurrentSubscription function...\n";
        getCurrentSubscription($mockUser);
        $output = ob_get_contents();
        ob_clean();
        echo "Function output: " . $output . "\n";
    } else {
        // Include the subscription management file to load functions
        include_once __DIR__ . '/api/subscription-management.php';
        echo "Subscription management file included\n";
    }
    
    ob_end_clean();
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
