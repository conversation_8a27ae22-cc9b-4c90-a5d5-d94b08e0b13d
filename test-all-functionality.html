<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Functionality Test - Bizma</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="config.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Bizma SaaS - Comprehensive Functionality Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- API Endpoints Test -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-blue-600">
                    <i class="fas fa-server mr-2"></i>API Endpoints
                </h2>
                <div id="api-results" class="space-y-2">
                    <div class="text-gray-600">Testing API endpoints...</div>
                </div>
                <button onclick="testAPIEndpoints()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Test APIs
                </button>
            </div>

            <!-- Header/Footer Test -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-600">
                    <i class="fas fa-layout mr-2"></i>Header & Footer
                </h2>
                <div id="layout-results" class="space-y-2">
                    <div class="text-gray-600">Testing layout components...</div>
                </div>
                <button onclick="testLayoutComponents()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Test Layout
                </button>
            </div>

            <!-- Subscription Components Test -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-purple-600">
                    <i class="fas fa-credit-card mr-2"></i>Subscriptions
                </h2>
                <div id="subscription-results" class="space-y-2">
                    <div class="text-gray-600">Testing subscription components...</div>
                </div>
                <button onclick="testSubscriptionComponents()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    Test Subscriptions
                </button>
            </div>

            <!-- Super Admin CMS Test -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-red-600">
                    <i class="fas fa-cog mr-2"></i>Super Admin CMS
                </h2>
                <div id="cms-results" class="space-y-2">
                    <div class="text-gray-600">Testing CMS components...</div>
                </div>
                <button onclick="testCMSComponents()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                    Test CMS
                </button>
            </div>

            <!-- Business Types Test -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-yellow-600">
                    <i class="fas fa-building mr-2"></i>Business Types
                </h2>
                <div id="business-types-results" class="space-y-2">
                    <div class="text-gray-600">Testing business types...</div>
                </div>
                <button onclick="testBusinessTypes()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                    Test Business Types
                </button>
            </div>

            <!-- Overall Status -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-600">
                    <i class="fas fa-chart-line mr-2"></i>Overall Status
                </h2>
                <div id="overall-status" class="space-y-2">
                    <div class="text-gray-600">Click "Test All" to run comprehensive test</div>
                </div>
                <button onclick="testAll()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Test All
                </button>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Detailed Test Results</h2>
            <div id="detailed-results" class="bg-gray-50 rounded p-4 max-h-96 overflow-y-auto">
                <div class="text-gray-600">No tests run yet. Click any test button above to start.</div>
            </div>
        </div>
    </div>

    <script>
        // Test API Endpoints
        async function testAPIEndpoints() {
            const resultsDiv = document.getElementById('api-results');
            const detailedDiv = document.getElementById('detailed-results');
            
            resultsDiv.innerHTML = '<div class="text-blue-600"><i class="fas fa-spinner fa-spin mr-2"></i>Testing...</div>';
            
            const endpoints = [
                '/biz/api/business-types.php',
                '/biz/api/pricing-plans.php',
                '/biz/api/api.php',
                '/biz/api/subscription-management.php'
            ];
            
            let results = [];
            let passed = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    const status = response.status;
                    const isSuccess = status === 200 || status === 401 || status === 403; // 401/403 means endpoint exists but needs auth
                    
                    if (isSuccess) passed++;
                    
                    results.push({
                        endpoint,
                        status,
                        success: isSuccess,
                        message: isSuccess ? 'Accessible' : 'Not Found'
                    });
                } catch (error) {
                    results.push({
                        endpoint,
                        status: 'Error',
                        success: false,
                        message: error.message
                    });
                }
            }
            
            // Update results
            resultsDiv.innerHTML = `
                <div class="${passed === endpoints.length ? 'text-green-600' : 'text-yellow-600'}">
                    <i class="fas fa-${passed === endpoints.length ? 'check' : 'exclamation-triangle'} mr-2"></i>
                    ${passed}/${endpoints.length} endpoints accessible
                </div>
            `;
            
            // Update detailed results
            let detailedHTML = '<h3 class="font-semibold mb-2">API Endpoints Test Results:</h3>';
            results.forEach(result => {
                detailedHTML += `
                    <div class="mb-2 p-2 rounded ${result.success ? 'bg-green-100' : 'bg-red-100'}">
                        <strong>${result.endpoint}</strong>: ${result.status} - ${result.message}
                    </div>
                `;
            });
            detailedDiv.innerHTML = detailedHTML;
        }

        // Test Layout Components
        function testLayoutComponents() {
            const resultsDiv = document.getElementById('layout-results');
            
            const components = [
                'WebsiteHeader',
                'WebsiteFooter', 
                'MainLayout',
                'Sidebar',
                'Header'
            ];
            
            let passed = 0;
            let results = [];
            
            components.forEach(component => {
                const exists = typeof window[component] !== 'undefined';
                if (exists) passed++;
                results.push({ component, exists });
            });
            
            resultsDiv.innerHTML = `
                <div class="${passed === components.length ? 'text-green-600' : 'text-yellow-600'}">
                    <i class="fas fa-${passed === components.length ? 'check' : 'exclamation-triangle'} mr-2"></i>
                    ${passed}/${components.length} layout components loaded
                </div>
            `;
        }

        // Test Subscription Components
        function testSubscriptionComponents() {
            const resultsDiv = document.getElementById('subscription-results');
            
            const components = [
                'SubscriptionStatus',
                'TrialStatusBanner',
                'TrialBanner',
                'ExtendTrialModal'
            ];
            
            let passed = 0;
            
            components.forEach(component => {
                if (typeof window[component] !== 'undefined') passed++;
            });
            
            resultsDiv.innerHTML = `
                <div class="${passed >= 2 ? 'text-green-600' : 'text-yellow-600'}">
                    <i class="fas fa-${passed >= 2 ? 'check' : 'exclamation-triangle'} mr-2"></i>
                    ${passed}/${components.length} subscription components loaded
                </div>
            `;
        }

        // Test CMS Components
        function testCMSComponents() {
            const resultsDiv = document.getElementById('cms-results');
            
            const components = [
                'PolicyPagesManager',
                'PolicyEditorModal',
                'PolicyPreviewModal',
                'SystemSettingsManager'
            ];
            
            let passed = 0;
            
            components.forEach(component => {
                if (typeof window[component] !== 'undefined') passed++;
            });
            
            resultsDiv.innerHTML = `
                <div class="${passed >= 2 ? 'text-green-600' : 'text-yellow-600'}">
                    <i class="fas fa-${passed >= 2 ? 'check' : 'exclamation-triangle'} mr-2"></i>
                    ${passed}/${components.length} CMS components loaded
                </div>
            `;
        }

        // Test Business Types
        async function testBusinessTypes() {
            const resultsDiv = document.getElementById('business-types-results');
            
            resultsDiv.innerHTML = '<div class="text-yellow-600"><i class="fas fa-spinner fa-spin mr-2"></i>Testing...</div>';
            
            try {
                const response = await fetch('/biz/api/business-types.php');
                const isSuccess = response.status === 200;
                
                if (isSuccess) {
                    const data = await response.json();
                    const hasData = data.success && data.data && data.data.length > 0;
                    
                    resultsDiv.innerHTML = `
                        <div class="${hasData ? 'text-green-600' : 'text-yellow-600'}">
                            <i class="fas fa-${hasData ? 'check' : 'exclamation-triangle'} mr-2"></i>
                            ${hasData ? `${data.data.length} business types loaded` : 'No business types found'}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="text-red-600">
                            <i class="fas fa-times mr-2"></i>
                            API not accessible (${response.status})
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="text-red-600">
                        <i class="fas fa-times mr-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            }
        }

        // Test All
        async function testAll() {
            const overallDiv = document.getElementById('overall-status');
            overallDiv.innerHTML = '<div class="text-blue-600"><i class="fas fa-spinner fa-spin mr-2"></i>Running all tests...</div>';
            
            await testAPIEndpoints();
            testLayoutComponents();
            testSubscriptionComponents();
            testCMSComponents();
            await testBusinessTypes();
            
            overallDiv.innerHTML = `
                <div class="text-green-600">
                    <i class="fas fa-check mr-2"></i>
                    All tests completed! Check individual results above.
                </div>
            `;
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testBusinessTypes();
            }, 1000);
        });
    </script>
</body>
</html>
