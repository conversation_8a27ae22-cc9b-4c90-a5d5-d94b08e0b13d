// ErrorMessage Component
function ErrorMessage({ error, onRetry }) {
        const errorMessage = typeof error === 'string' ? error : (error?.message || 'Something went wrong. Please try again.');
        
        return (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <i className="fas fa-exclamation-circle text-red-500"></i>
                    </div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                            An error occurred
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                            <p>{errorMessage}</p>
                        </div>
                        {onRetry && (
                            <div className="mt-4">
                                <button
                                    type="button"
                                    onClick={onRetry}
                                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                    Try again
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
}

// Make ErrorMessage globally available
window.ErrorMessage = ErrorMessage;