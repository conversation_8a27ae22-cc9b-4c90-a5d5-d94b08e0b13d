// Make LoginForm component globally available
window.LoginForm = function LoginForm({ onLogin, onSwitchToRegister, onSwitchToForgotPassword }) {
    try {
        const [formData, setFormData] = React.useState({
            email: '',
            password: '',
            rememberMe: false
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.email) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(formData.email)) {
                newErrors.email = 'Invalid email format';
            }
            if (!formData.password) {
                newErrors.password = 'Password is required';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                setErrors({});

                console.log('Login attempt with:', { email: formData.email, rememberMe: formData.rememberMe });

                // Use direct API call to simple-auth.php
                const response = await fetch('/api/simple-auth.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'login',
                        email: formData.email,
                        password: formData.password,
                        remember_me: formData.rememberMe
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Login response:', data);

                if (data.success && data.token) {
                    // Store token and user data
                    localStorage.setItem('authToken', data.token);
                    if (formData.rememberMe) {
                        localStorage.setItem('rememberMe', 'true');
                    }
                    onLogin(data.user, data.token);
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                setErrors({ submit: error.message || 'Invalid email or password' });
            } finally {
                setLoading(false);
            }
        };

        // Check if this is being used in a modal (has onSwitchToRegister prop)
        const isModal = !!onSwitchToRegister;

        return (
            <div data-name="login-form" className={isModal ? "p-6" : "min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"}>
                <div className={isModal ? "w-full space-y-6" : "max-w-md w-full space-y-8"}>
                    <div>
                        <h2 className={`text-center font-extrabold text-gray-900 ${isModal ? 'text-2xl mb-4' : 'mt-6 text-3xl'}`}>
                            Sign in to your account
                        </h2>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            Or{' '}
                            {onSwitchToRegister ? (
                                <button
                                    onClick={onSwitchToRegister}
                                    className="font-medium text-blue-600 hover:text-blue-500"
                                >
                                    start your 14-day free trial
                                </button>
                            ) : (
                                <a href="/register" className="font-medium text-blue-600 hover:text-blue-500">
                                    start your 14-day free trial
                                </a>
                            )}
                        </p>
                    </div>

                    <form className={isModal ? "mt-6 space-y-4" : "mt-8 space-y-6"} onSubmit={handleSubmit}>
                        <div className="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label htmlFor="email" className="sr-only">Email address</label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.email ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Email address"
                                />
                                {errors.email && (
                                    <p className="mt-1 text-xs text-red-600">{errors.email}</p>
                                )}
                            </div>
                            <div>
                                <label htmlFor="password" className="sr-only">Password</label>
                                <input
                                    id="password"
                                    name="password"
                                    type="password"
                                    autoComplete="current-password"
                                    required
                                    value={formData.password}
                                    onChange={handleInputChange}
                                    className={`appearance-none rounded-none relative block w-full px-3 py-2 border ${
                                        errors.password ? 'border-red-300' : 'border-gray-300'
                                    } placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                                    placeholder="Password"
                                />
                                {errors.password && (
                                    <p className="mt-1 text-xs text-red-600">{errors.password}</p>
                                )}
                            </div>
                        </div>

                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <input
                                    id="remember-me"
                                    name="rememberMe"
                                    type="checkbox"
                                    checked={formData.rememberMe}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                                    Remember me
                                </label>
                            </div>

                            <div className="text-sm">
                                {onSwitchToForgotPassword ? (
                                    <button
                                        onClick={onSwitchToForgotPassword}
                                        className="font-medium text-blue-600 hover:text-blue-500"
                                    >
                                        Forgot your password?
                                    </button>
                                ) : (
                                    <a href="/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                                        Forgot your password?
                                    </a>
                                )}
                            </div>
                        </div>

                        {errors.submit && (
                            <div className="text-red-600 text-sm text-center">
                                {errors.submit}
                            </div>
                        )}

                        <div>
                            <Button
                                type="submit"
                                loading={loading}
                                disabled={loading}
                                className="w-full"
                            >
                                Sign in
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        );
    } catch (error) {
        console.error('LoginForm component error:', error);
        reportError(error);
        return null;
    }
}
