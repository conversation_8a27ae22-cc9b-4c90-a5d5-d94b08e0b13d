<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2563eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        .error {
            color: #dc2626;
            margin-top: 10px;
        }
        .success {
            color: #16a34a;
            margin-top: 10px;
        }
        pre {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Registration Form</h1>
    <p>Use this form to test the registration API directly.</p>
    
    <div class="form-group">
        <label for="name">Full Name</label>
        <input type="text" id="name" name="name" value="Test User">
    </div>
    
    <div class="form-group">
        <label for="email">Email</label>
        <input type="email" id="email" name="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" value="TestPassword123">
    </div>
    
    <div class="form-group">
        <label for="company_name">Company Name</label>
        <input type="text" id="company_name" name="company_name" value="Test Company">
    </div>
    
    <div class="form-group">
        <label for="company_size">Company Size</label>
        <select id="company_size" name="company_size">
            <option value="1-10">1-10 employees</option>
            <option value="11-50">11-50 employees</option>
            <option value="51-200">51-200 employees</option>
            <option value="201-500">201-500 employees</option>
            <option value="501+">501+ employees</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="industry">Industry</label>
        <select id="industry" name="industry">
            <option value="Technology">Technology</option>
            <option value="Finance">Finance</option>
            <option value="Healthcare">Healthcare</option>
            <option value="Education">Education</option>
            <option value="Retail">Retail</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Other">Other</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="selected_plan">Plan</label>
        <select id="selected_plan" name="selected_plan">
            <option value="basic">Basic Plan</option>
        </select>
    </div>
    
    <button id="submit-btn">Register</button>
    
    <div id="error" class="error" style="display: none;"></div>
    <div id="success" class="success" style="display: none;"></div>
    
    <h2>Response:</h2>
    <pre id="response">No response yet</pre>
    
    <script>
        document.getElementById('submit-btn').addEventListener('click', async function() {
            const errorDiv = document.getElementById('error');
            const successDiv = document.getElementById('success');
            const responseDiv = document.getElementById('response');
            
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            responseDiv.textContent = 'Sending request...';
            
            // Get form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                company_name: document.getElementById('company_name').value,
                company_size: document.getElementById('company_size').value,
                industry: document.getElementById('industry').value,
                selected_plan: document.getElementById('selected_plan').value,
                billing_cycle: 'monthly'
            };
            
            try {
                // Generate a unique email to avoid duplicates
                const timestamp = new Date().getTime();
                formData.email = `test${timestamp}@example.com`;
                document.getElementById('email').value = formData.email;
                
                // Send request
                const response = await fetch('api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                // Get response as text first
                const responseText = await response.text();
                responseDiv.textContent = responseText;
                
                // Try to parse as JSON
                try {
                    const data = JSON.parse(responseText);
                    
                    // Format JSON response
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    
                    if (data.success) {
                        successDiv.textContent = 'Registration successful!';
                        successDiv.style.display = 'block';
                    } else {
                        errorDiv.textContent = data.message || 'Registration failed';
                        errorDiv.style.display = 'block';
                    }
                } catch (parseError) {
                    errorDiv.textContent = 'Failed to parse response as JSON';
                    errorDiv.style.display = 'block';
                }
                
            } catch (error) {
                console.error('Request error:', error);
                errorDiv.textContent = 'Network error: ' + error.message;
                errorDiv.style.display = 'block';
                responseDiv.textContent = 'Request failed: ' + error.message;
            }
        });
    </script>
</body>
</html>