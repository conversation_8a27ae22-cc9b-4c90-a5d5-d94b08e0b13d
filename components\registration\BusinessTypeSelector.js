// Business Type Selector Component
function BusinessTypeSelector({ selectedType, onTypeSelect, businessTypes = [] }) {
    const [loading, setLoading] = React.useState(true);
    const [types, setTypes] = React.useState(businessTypes);
    const [error, setError] = React.useState(null);

    // Default business types if none provided
    const defaultTypes = [
        {
            id: 'jewellery',
            name: 'Jewellery Business',
            description: 'Specialized for jewellery stores, designers, and manufacturers',
            icon: 'fas fa-gem',
            features: ['Precious metals tracking', 'Stone certification', 'Custom design tools', 'Repair tracking'],
            color: 'purple'
        },
        {
            id: 'retail',
            name: 'Retail Business',
            description: 'Perfect for retail stores, shops, and e-commerce businesses',
            icon: 'fas fa-store',
            features: ['Barcode scanning', 'Multi-location support', 'Loyalty programs', 'Discount management'],
            color: 'blue'
        },
        {
            id: 'education',
            name: 'Education Services',
            description: 'Designed for schools, training centers, and educational institutions',
            icon: 'fas fa-graduation-cap',
            features: ['Student portal', 'Grade management', 'Attendance tracking', 'Course materials'],
            color: 'green'
        },
        {
            id: 'healthcare',
            name: 'Healthcare Services',
            description: 'Tailored for clinics, hospitals, and healthcare providers',
            icon: 'fas fa-heartbeat',
            features: ['Patient records', 'Appointment scheduling', 'Prescription management', 'Insurance billing'],
            color: 'red'
        },
        {
            id: 'consulting',
            name: 'Consulting Services',
            description: 'Ideal for consultants, agencies, and professional services',
            icon: 'fas fa-briefcase',
            features: ['Project management', 'Time billing', 'Proposal generation', 'Client portal'],
            color: 'indigo'
        },
        {
            id: 'manufacturing',
            name: 'Manufacturing',
            description: 'Built for manufacturers, suppliers, and production companies',
            icon: 'fas fa-industry',
            features: ['Production planning', 'Quality control', 'Supplier management', 'Batch tracking'],
            color: 'gray'
        }
    ];

    React.useEffect(() => {
        fetchBusinessTypes();
    }, []);

    const fetchBusinessTypes = async () => {
        try {
            setLoading(true);
            
            // Try to fetch from API first
            const response = await fetch('/biz/api/business-types');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    setTypes(data.data);
                } else {
                    setTypes(defaultTypes);
                }
            } else {
                // Fallback to default types
                setTypes(defaultTypes);
            }
        } catch (error) {
            console.warn('Failed to fetch business types, using defaults:', error);
            setTypes(defaultTypes);
        } finally {
            setLoading(false);
        }
    };

    const handleTypeSelect = (type) => {
        if (onTypeSelect) {
            onTypeSelect(type);
        }
    };

    if (loading) {
        return (
            <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading business types...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <div className="text-red-600 mb-4">
                    <i className="fas fa-exclamation-triangle text-2xl"></i>
                </div>
                <p className="text-red-600">{error}</p>
                <button 
                    onClick={fetchBusinessTypes}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                    Try Again
                </button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    What type of business do you run?
                </h2>
                <p className="text-gray-600">
                    We'll customize your experience based on your business type
                </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                {types.map((type) => (
                    <div
                        key={type.id}
                        onClick={() => handleTypeSelect(type)}
                        className={`relative cursor-pointer rounded-lg border-2 p-6 hover:shadow-lg transition-all ${
                            selectedType?.id === type.id
                                ? `border-${type.color}-500 bg-${type.color}-50 shadow-lg`
                                : 'border-gray-200 hover:border-gray-300'
                        }`}
                    >
                        {/* Selection indicator */}
                        {selectedType?.id === type.id && (
                            <div className={`absolute top-4 right-4 w-6 h-6 bg-${type.color}-500 rounded-full flex items-center justify-center`}>
                                <i className="fas fa-check text-white text-sm"></i>
                            </div>
                        )}

                        {/* Icon */}
                        <div className={`w-12 h-12 bg-${type.color}-100 rounded-lg flex items-center justify-center mb-4`}>
                            <i className={`${type.icon} text-${type.color}-600 text-xl`}></i>
                        </div>

                        {/* Content */}
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {type.name}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4">
                            {type.description}
                        </p>

                        {/* Features */}
                        <div className="space-y-2">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                                Key Features:
                            </p>
                            <ul className="space-y-1">
                                {(type.features || []).slice(0, 3).map((feature, index) => (
                                    <li key={index} className="flex items-center text-sm text-gray-600">
                                        <i className={`fas fa-check text-${type.color}-500 text-xs mr-2`}></i>
                                        {feature}
                                    </li>
                                ))}
                                {(type.features || []).length > 3 && (
                                    <li className="text-sm text-gray-500">
                                        +{(type.features || []).length - 3} more features
                                    </li>
                                )}
                            </ul>
                        </div>
                    </div>
                ))}
            </div>

            {/* Custom option */}
            <div className="text-center pt-6 border-t border-gray-200">
                <button
                    onClick={() => handleTypeSelect({ id: 'other', name: 'Other', description: 'General business setup' })}
                    className={`px-6 py-3 rounded-lg border-2 transition-all ${
                        selectedType?.id === 'other'
                            ? 'border-gray-500 bg-gray-50 shadow-lg'
                            : 'border-gray-300 hover:border-gray-400'
                    }`}
                >
                    <i className="fas fa-ellipsis-h text-gray-600 mr-2"></i>
                    <span className="text-gray-700">Other / General Business</span>
                </button>
            </div>
        </div>
    );
}

// Make component globally available
window.BusinessTypeSelector = BusinessTypeSelector;
