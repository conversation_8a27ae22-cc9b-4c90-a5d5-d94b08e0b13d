<?php
/**
 * Comprehensive API Endpoint Testing Script
 * Tests all major API endpoints and reports their status
 */

require_once 'db-config.php';
require_once 'utils/ErrorHandler.php';

ErrorHandler::init();

echo "=== API Endpoint Testing ===\n\n";

// Test endpoints
$endpoints = [
    // Authentication endpoints
    'auth/login' => ['method' => 'POST', 'description' => 'User login'],
    'auth/verify' => ['method' => 'POST', 'description' => 'Token verification'],
    
    // CRUD endpoints
    'lead' => ['method' => 'GET', 'description' => 'List leads'],
    'customer' => ['method' => 'GET', 'description' => 'List customers'],
    'item' => ['method' => 'GET', 'description' => 'List items'],
    'invoice' => ['method' => 'GET', 'description' => 'List invoices'],
    'quotation' => ['method' => 'GET', 'description' => 'List quotations'],
    'contract' => ['method' => 'GET', 'description' => 'List contracts'],
    
    // Subscription management
    'subscription-management/current' => ['method' => 'GET', 'description' => 'Current subscription'],
    'subscription-management/trial-status' => ['method' => 'GET', 'description' => 'Trial status'],
    
    // Super admin endpoints
    'super-admin/companies' => ['method' => 'GET', 'description' => 'List companies (Super Admin)'],
    'super-admin/subscriptions/list' => ['method' => 'GET', 'description' => 'List subscriptions (Super Admin)'],
    'super-admin/policy-pages' => ['method' => 'GET', 'description' => 'Policy pages (Super Admin)'],
];

$results = [];
$baseUrl = 'http://localhost/biz/api/api.php/';

// Function to test endpoint
function testEndpoint($url, $method = 'GET', $headers = []) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// Get a valid auth token for testing authenticated endpoints
function getAuthToken() {
    global $conn;
    
    // Get a valid token from the database
    $sql = "SELECT auth_token FROM users WHERE auth_token IS NOT NULL AND token_expires > NOW() LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['auth_token'];
    }
    
    return null;
}

$authToken = getAuthToken();
$authHeaders = $authToken ? ['Authorization: Bearer ' . $authToken] : [];

echo "Auth token available: " . ($authToken ? "✅ Yes" : "❌ No") . "\n\n";

// Test each endpoint
foreach ($endpoints as $endpoint => $config) {
    $url = $baseUrl . $endpoint;
    $method = $config['method'];
    $description = $config['description'];
    
    echo "Testing: $description ($method $endpoint)\n";
    
    // Determine if endpoint needs authentication
    $needsAuth = !in_array($endpoint, ['auth/login', 'auth/verify']);
    $headers = $needsAuth ? $authHeaders : [];
    
    $result = testEndpoint($url, $method, $headers);
    
    $status = 'UNKNOWN';
    $message = '';
    
    if (!empty($result['error'])) {
        $status = 'ERROR';
        $message = $result['error'];
    } else {
        switch ($result['http_code']) {
            case 200:
                $status = 'SUCCESS';
                $message = 'OK';
                break;
            case 401:
                $status = 'AUTH_REQUIRED';
                $message = 'Authentication required';
                break;
            case 403:
                $status = 'FORBIDDEN';
                $message = 'Access denied';
                break;
            case 404:
                $status = 'NOT_FOUND';
                $message = 'Endpoint not found';
                break;
            case 405:
                $status = 'METHOD_NOT_ALLOWED';
                $message = 'Method not allowed';
                break;
            case 500:
                $status = 'SERVER_ERROR';
                $message = 'Internal server error';
                break;
            default:
                $status = 'HTTP_' . $result['http_code'];
                $message = 'HTTP ' . $result['http_code'];
        }
    }
    
    $icon = $status === 'SUCCESS' ? '✅' : 
           ($status === 'AUTH_REQUIRED' && $needsAuth && !$authToken ? '⚠️' : '❌');
    
    echo "  $icon Status: $status ($message)\n";
    
    // Try to parse JSON response for additional info
    if ($result['response']) {
        $jsonData = json_decode($result['response'], true);
        if ($jsonData && isset($jsonData['error'])) {
            echo "  📝 Error: " . $jsonData['error'] . "\n";
        } elseif ($jsonData && isset($jsonData['success'])) {
            echo "  📝 Success: " . ($jsonData['success'] ? 'true' : 'false') . "\n";
        }
    }
    
    $results[$endpoint] = [
        'status' => $status,
        'http_code' => $result['http_code'],
        'message' => $message
    ];
    
    echo "\n";
}

// Summary
echo "=== Testing Summary ===\n";

$statusCounts = [];
foreach ($results as $endpoint => $result) {
    $status = $result['status'];
    $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
}

foreach ($statusCounts as $status => $count) {
    echo "$status: $count endpoints\n";
}

echo "\n=== Detailed Results ===\n";
foreach ($results as $endpoint => $result) {
    $icon = $result['status'] === 'SUCCESS' ? '✅' : 
           ($result['status'] === 'AUTH_REQUIRED' ? '⚠️' : '❌');
    echo "$icon $endpoint: {$result['status']} ({$result['http_code']})\n";
}

echo "\n✅ Endpoint testing completed!\n";

// Cleanup
$conn->close();
?>
