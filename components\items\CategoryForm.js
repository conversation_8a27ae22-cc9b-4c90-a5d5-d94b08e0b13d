function CategoryForm({ category, isSubcategory = false, parentCategoryId = null, onSubmit, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            name: category && category.name ? category.name : '',
            description: category && category.description ? category.description : ''
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [suggestedCategories, setSuggestedCategories] = React.useState([]);

        React.useEffect(() => {
            // Prepare suggested categories based on whether it's a category or subcategory
            if (isSubcategory) {
                if (parentCategoryId) {
                    // Load suggested subcategories based on parent category
                    loadSuggestedSubcategories(parentCategoryId);
                }
            } else {
                // Suggested main categories
                setSuggestedCategories([
                    { name: 'Digital Marketing', description: 'Online marketing services including social media, SEO, and content marketing' },
                    { name: 'Web Development', description: 'Website design, development, and maintenance services' },
                    { name: 'IT Services', description: 'Technical support, infrastructure, and managed IT services' },
                    { name: 'Consulting', description: 'Business strategy, financial, and operational consulting services' },
                    { name: 'Products', description: 'Physical products and merchandise' }
                ]);
            }
        }, [isSubcategory, parentCategoryId]);

        const loadSuggestedSubcategories = async (parentId) => {
            try {
                // Get parent category details to determine appropriate suggestions
                const token = localStorage.getItem('authToken');
                const response = await fetch(`/api/api.php/item_category/${parentId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const parent = await response.json();
                    const parentName = parent.objectData.name;
                
                // Suggested subcategories based on parent category
                if (parentName.toLowerCase().includes('digital marketing')) {
                    setSuggestedCategories([
                        { name: 'Social Media Management', description: 'Management of social media accounts and posting schedules' },
                        { name: 'SEO Services', description: 'Search engine optimization to improve website rankings' },
                        { name: 'PPC Advertising', description: 'Pay-per-click advertising campaigns on search engines and social platforms' },
                        { name: 'Content Marketing', description: 'Blog posts, articles, and content creation services' },
                        { name: 'Email Marketing', description: 'Email campaign design, execution, and analysis' },
                        { name: 'Analytics & Reporting', description: 'Performance tracking and detailed reporting services' }
                    ]);
                } else if (parentName.toLowerCase().includes('web')) {
                    setSuggestedCategories([
                        { name: 'Website Design', description: 'UI/UX design for websites' },
                        { name: 'Frontend Development', description: 'Client-side web development services' },
                        { name: 'Backend Development', description: 'Server-side programming and API development' },
                        { name: 'E-commerce Solutions', description: 'Online store setup and management' },
                        { name: 'Website Maintenance', description: 'Regular updates, security patches, and monitoring' }
                    ]);
                } else {
                    setSuggestedCategories([]);
                }
                } else {
                    throw new Error('Failed to fetch parent category');
                }
            } catch (error) {
                console.error('Error loading parent category:', error);
                setSuggestedCategories([]);
            }
        };

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));

            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        const applySuggestedCategory = (suggested) => {
            setFormData({
                name: suggested.name,
                description: suggested.description
            });
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.name) {
                newErrors.name = `${isSubcategory ? 'Subcategory' : 'Category'} name is required`;
            }
            
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const categoryData = {
                    ...formData,
                    updatedAt: new Date().toISOString()
                };

                // Determine the object type based on whether it's a category or subcategory
                const objectType = isSubcategory && parentCategoryId 
                    ? `item_subcategory:${parentCategoryId}` 
                    : 'item_category';

                const token = localStorage.getItem('authToken');
                let response;

                if (category && category.id) {
                    // Update existing category
                    response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/${objectType}/${category.id}`, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(categoryData)
                    });
                } else {
                    // Create new category
                    response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/${objectType}`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(categoryData)
                    });
                }

                if (response.ok) {
                    onSubmit();
                } else {
                    throw new Error(`Failed to save ${isSubcategory ? 'subcategory' : 'category'}`);
                }
            } catch (error) {
                console.error(`Error saving ${isSubcategory ? 'subcategory' : 'category'}:`, error);
                setErrors({ submit: `Failed to save ${isSubcategory ? 'subcategory' : 'category'}` });
            } finally {
                setLoading(false);
            }
        };

        return (
            <form data-name="category-form" onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700">
                        {isSubcategory ? 'Subcategory' : 'Category'} Name <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700">
                        Description
                    </label>
                    <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        rows={4}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                </div>

                {suggestedCategories.length > 0 && (
                    <div>
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Suggested {isSubcategory ? 'Subcategories' : 'Categories'}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {suggestedCategories.map((suggested, index) => (
                                <div 
                                    key={index} 
                                    className="p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer"
                                    onClick={() => applySuggestedCategory(suggested)}
                                >
                                    <h4 className="font-medium text-sm">{suggested.name}</h4>
                                    <p className="text-xs text-gray-500">{suggested.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {errors.submit && (
                    <div className="text-red-600 text-sm mt-2">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {category && category.id ? `Update ${isSubcategory ? 'Subcategory' : 'Category'}` : `Create ${isSubcategory ? 'Subcategory' : 'Category'}`}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('CategoryForm component error:', error);
        reportError(error);
        return null;
    }
}
