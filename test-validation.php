<?php
// Include the validation handler
require_once 'api/handlers/validation-handler.php';

// Test with null value
$testData = [
    'name' => 'Test Name',
    'email' => '<EMAIL>',
    'notes' => null,
    'items' => [
        ['name' => 'Item 1', 'description' => null],
        ['name' => 'Item 2', 'description' => 'Test description']
    ]
];

// Process the data
$result = ValidationHandler::processObjectData('customer', $testData);

// Output the result
echo "Validation result: " . ($result['valid'] ? 'Valid' : 'Invalid') . "\n";
echo "Processed data:\n";
print_r($result['data']);

if (!$result['valid']) {
    echo "Validation errors:\n";
    print_r($result['errors']);
}

echo "Test completed successfully!\n";
?>