// Make LandingPage component globally available
window.LandingPage = function LandingPage() {
    try {
        const [showLoginModal, setShowLoginModal] = React.useState(false);
        const [showForgotPasswordModal, setShowForgotPasswordModal] = React.useState(false);

        const features = [
            {
                icon: 'fas fa-users',
                title: 'Customer Management',
                description: 'Organize and manage your customer relationships with powerful CRM tools.'
            },
            {
                icon: 'fas fa-file-invoice-dollar',
                title: 'Smart Invoicing',
                description: 'Create professional invoices and quotations with automated calculations.'
            },
            {
                icon: 'fas fa-chart-line',
                title: 'Business Analytics',
                description: 'Get insights into your business performance with detailed reports and analytics.'
            },
            {
                icon: 'fas fa-mobile-alt',
                title: 'Mobile Ready',
                description: 'Access your business data anywhere, anytime with our responsive design.'
            },
            {
                icon: 'fas fa-shield-alt',
                title: 'Secure & Reliable',
                description: 'Enterprise-grade security with 99.9% uptime guarantee for your peace of mind.'
            },
            {
                icon: 'fas fa-headset',
                title: '24/7 Support',
                description: 'Get help when you need it with our dedicated customer support team.'
            }
        ];



        const handleGetStarted = () => {
            window.location.href = '/biz/register.html';
        };

        const handleLogin = () => {
            setShowLoginModal(true);
        };

        const scrollToSection = (sectionId) => {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        };

        return (
            <div className="bg-white">
                {/* Hero Section */}
                <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                                Grow Your Business with
                                <span className="text-blue-600"> Bizma</span>
                            </h1>
                            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                                The all-in-one business management platform that helps you manage customers, 
                                create invoices, track leads, and grow your business efficiently.
                            </p>
                            {/* Trial Highlight */}
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8 inline-block">
                                <div className="flex items-center justify-center space-x-2 text-green-700">
                                    <i className="fas fa-gift text-green-600"></i>
                                    <span className="font-semibold">14-Day Free Trial</span>
                                    <span className="text-green-600">• No Credit Card Required</span>
                                </div>
                            </div>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={handleGetStarted}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors shadow-lg"
                                >
                                    <i className="fas fa-rocket mr-2"></i>
                                    Start Free Trial
                                </button>
                                <button
                                    onClick={() => scrollToSection('pricing')}
                                    className="border border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors"
                                >
                                    <i className="fas fa-tag mr-2"></i>
                                    View Pricing
                                </button>
                            </div>

                            {/* Trust Indicators */}
                            <div className="mt-8 flex flex-wrap justify-center items-center gap-6 text-gray-500">
                                <div className="flex items-center space-x-2">
                                    <i className="fas fa-shield-alt text-green-500"></i>
                                    <span className="text-sm">Secure & Reliable</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <i className="fas fa-users text-blue-500"></i>
                                    <span className="text-sm">1000+ Businesses</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <i className="fas fa-star text-yellow-500"></i>
                                    <span className="text-sm">4.9/5 Rating</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <i className="fas fa-clock text-purple-500"></i>
                                    <span className="text-sm">24/7 Support</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Features Section */}
                <section id="features" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Everything you need to run your business
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Bizma provides all the tools you need to manage your business operations 
                                efficiently and scale your growth.
                            </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {features.map((feature, index) => (
                                <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow">
                                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i className={`${feature.icon} text-2xl text-blue-600`}></i>
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                                    <p className="text-gray-600">{feature.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Trial Benefits Section */}
                <section className="py-16 bg-blue-600 text-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4">
                                Start Your Free Trial Today
                            </h2>
                            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
                                Experience the full power of Bizma with our 14-day free trial.
                                No credit card required, no hidden fees.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                            <div className="text-center">
                                <div className="bg-blue-500 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <i className="fas fa-rocket text-2xl"></i>
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Instant Setup</h3>
                                <p className="text-blue-100">Get started in minutes with our guided setup process</p>
                            </div>

                            <div className="text-center">
                                <div className="bg-blue-500 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <i className="fas fa-tools text-2xl"></i>
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Full Features</h3>
                                <p className="text-blue-100">Access all premium features during your trial period</p>
                            </div>

                            <div className="text-center">
                                <div className="bg-blue-500 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                    <i className="fas fa-headset text-2xl"></i>
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Expert Support</h3>
                                <p className="text-blue-100">Get help from our support team whenever you need it</p>
                            </div>
                        </div>

                        <div className="text-center">
                            <button
                                onClick={handleGetStarted}
                                className="bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg"
                            >
                                <i className="fas fa-play mr-2"></i>
                                Start Your Free Trial
                            </button>
                            <p className="text-sm text-blue-200 mt-4">
                                Join thousands of businesses already using Bizma
                            </p>
                        </div>
                    </div>
                </section>

                {/* Pricing Section */}
                <section id="pricing" className="py-20 bg-gray-50">
                    <EnhancedPricingPlans
                        onPlanSelect={(planData) => {
                            console.log('Plan selected:', planData);
                            // Store selected plan in localStorage for registration
                            localStorage.setItem('selectedPlan', JSON.stringify(planData));
                            handleGetStarted();
                        }}
                        showTitle={true}
                    />
                </section>

                {/* About Section */}
                <section id="about" className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Why choose Bizma?
                            </h2>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Built by entrepreneurs, for entrepreneurs. We understand the challenges 
                                of running a business and have created the perfect solution.
                            </p>
                        </div>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                                    Trusted by thousands of businesses
                                </h3>
                                <div className="space-y-4">
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">99.9% uptime guarantee</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">Bank-level security</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">24/7 customer support</span>
                                    </div>
                                    <div className="flex items-center">
                                        <i className="fas fa-check-circle text-green-500 mr-3"></i>
                                        <span className="text-gray-700">Regular feature updates</span>
                                    </div>
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="bg-blue-50 rounded-lg p-8">
                                    <i className="fas fa-rocket text-4xl text-blue-600 mb-4"></i>
                                    <h4 className="text-xl font-semibold text-gray-900 mb-2">
                                        Ready to get started?
                                    </h4>
                                    <p className="text-gray-600 mb-6">
                                        Join thousands of businesses already using Bizma to grow their operations.
                                    </p>
                                    <button
                                        onClick={handleGetStarted}
                                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
                                    >
                                        Start Your Free Trial
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Login Modal */}
                {showLoginModal && (
                    <Modal
                        isOpen={showLoginModal}
                        onClose={() => setShowLoginModal(false)}
                        title="Sign In to Bizma"
                        size="sm"
                    >
                        <LoginForm
                            onLogin={() => {
                                setShowLoginModal(false);
                                // Redirect to dashboard after login
                                const dashboardUrl = window.getAppUrl ? window.getAppUrl('/dashboard') : '/dashboard';
                                window.location.href = dashboardUrl;
                            }}
                            onSwitchToRegister={() => {
                                setShowLoginModal(false);
                                window.location.href = '/biz/register.html';
                            }}
                            onSwitchToForgotPassword={() => {
                                setShowLoginModal(false);
                                setShowForgotPasswordModal(true);
                            }}
                        />
                    </Modal>
                )}

                {/* Forgot Password Modal */}
                {showForgotPasswordModal && (
                    <Modal
                        isOpen={showForgotPasswordModal}
                        onClose={() => setShowForgotPasswordModal(false)}
                        title="Reset Your Password"
                        size="sm"
                    >
                        <ForgotPasswordForm
                            onSuccess={() => {
                                setShowForgotPasswordModal(false);
                                if (window.toast) {
                                    window.toast.success('Password reset email sent! Please check your inbox.');
                                }
                            }}
                            onSwitchToLogin={() => {
                                setShowForgotPasswordModal(false);
                                setShowLoginModal(true);
                            }}
                        />
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Landing page error:', error);
        // Report error to error handler if available
        if (window.ErrorHandler && window.ErrorHandler.handleError) {
            window.ErrorHandler.handleError(error);
        }
        return null;
    }
}
