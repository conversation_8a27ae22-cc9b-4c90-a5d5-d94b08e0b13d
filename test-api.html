<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Bizma</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">API Endpoint Tests</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Pricing Plans Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Pricing Plans API</h2>
                <button onclick="testPricingPlans()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Test Pricing Plans
                </button>
                <div id="pricing-result" class="mt-4 p-3 bg-gray-50 rounded text-sm"></div>
            </div>
            
            <!-- Business Types Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Business Types API</h2>
                <button onclick="testBusinessTypes()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Test Business Types
                </button>
                <div id="business-result" class="mt-4 p-3 bg-gray-50 rounded text-sm"></div>
            </div>
            
            <!-- Login Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Login API</h2>
                <input type="email" id="test-email" placeholder="Email" class="w-full p-2 border rounded mb-2" value="<EMAIL>">
                <input type="password" id="test-password" placeholder="Password" class="w-full p-2 border rounded mb-2" value="password123">
                <button onclick="testLogin()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Test Login
                </button>
                <div id="login-result" class="mt-4 p-3 bg-gray-50 rounded text-sm"></div>
            </div>
            
            <!-- Registration Test -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">Registration API</h2>
                <button onclick="testRegistration()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    Test Registration
                </button>
                <div id="register-result" class="mt-4 p-3 bg-gray-50 rounded text-sm"></div>
            </div>
        </div>
    </div>

    <script>
        async function testPricingPlans() {
            const resultDiv = document.getElementById('pricing-result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/biz/api/pricing-plans.php');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                resultDiv.className = 'mt-4 p-3 bg-green-50 rounded text-sm';
            } catch (error) {
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                resultDiv.className = 'mt-4 p-3 bg-red-50 rounded text-sm';
            }
        }
        
        async function testBusinessTypes() {
            const resultDiv = document.getElementById('business-result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/biz/api/business-types.php');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                resultDiv.className = 'mt-4 p-3 bg-green-50 rounded text-sm';
            } catch (error) {
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                resultDiv.className = 'mt-4 p-3 bg-red-50 rounded text-sm';
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/biz/api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                resultDiv.className = response.ok ? 'mt-4 p-3 bg-green-50 rounded text-sm' : 'mt-4 p-3 bg-yellow-50 rounded text-sm';
            } catch (error) {
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                resultDiv.className = 'mt-4 p-3 bg-red-50 rounded text-sm';
            }
        }
        
        async function testRegistration() {
            const resultDiv = document.getElementById('register-result');
            resultDiv.innerHTML = 'Testing...';
            
            const testData = {
                name: 'Test User',
                email: 'test' + Date.now() + '@example.com',
                password: 'password123',
                company_name: 'Test Company',
                business_type: 'retail'
            };
            
            try {
                const response = await fetch('/biz/api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Test Data:</strong> <pre>${JSON.stringify(testData, null, 2)}</pre>
                    <strong>Response:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                resultDiv.className = response.ok ? 'mt-4 p-3 bg-green-50 rounded text-sm' : 'mt-4 p-3 bg-yellow-50 rounded text-sm';
            } catch (error) {
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
                resultDiv.className = 'mt-4 p-3 bg-red-50 rounded text-sm';
            }
        }
    </script>
</body>
</html>
