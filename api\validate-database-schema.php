<?php
/**
 * Database Schema Validation Script
 * Ensures all required tables exist with proper structure and creates missing ones
 */

require_once 'db-config.php';
require_once 'utils/ErrorHandler.php';

ErrorHandler::init();

echo "=== Database Schema Validation ===\n\n";

// Required tables with their essential columns
$requiredTables = [
    'users' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'name' => 'varchar(255) NOT NULL',
        'email' => 'varchar(255) UNIQUE NOT NULL',
        'password_hash' => 'varchar(255) NOT NULL',
        'auth_token' => 'varchar(255)',
        'token_expires' => 'datetime',
        'status' => 'enum("active","inactive","suspended") DEFAULT "active"',
        'role' => 'varchar(50) DEFAULT "user"',
        'company_id' => 'varchar(255)',
        'last_login' => 'datetime',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'companies' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'name' => 'varchar(255) NOT NULL',
        'email' => 'varchar(255)',
        'phone' => 'varchar(50)',
        'address' => 'text',
        'website' => 'varchar(255)',
        'owner_id' => 'varchar(255)',
        'status' => 'enum("active","inactive","suspended") DEFAULT "active"',
        'subscription_plan' => 'varchar(50)',
        'business_type' => 'varchar(100)',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'leads' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'name' => 'varchar(255) NOT NULL',
        'email' => 'varchar(255)',
        'phone' => 'varchar(50)',
        'company' => 'varchar(255)',
        'status' => 'enum("new","contacted","qualified","proposal","negotiation","closed_won","closed_lost") DEFAULT "new"',
        'priority' => 'enum("low","medium","high") DEFAULT "medium"',
        'value' => 'decimal(10,2)',
        'notes' => 'text',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'subscriptions' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'plan_id' => 'varchar(255) NOT NULL',
        'status' => 'enum("trial","active","cancelled","expired") DEFAULT "trial"',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'trial_end_date' => 'datetime',
        'amount' => 'decimal(10,2)',
        'billing_cycle' => 'enum("monthly","yearly") DEFAULT "monthly"',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'customers' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'name' => 'varchar(255) NOT NULL',
        'email' => 'varchar(255)',
        'phone' => 'varchar(50)',
        'address' => 'text',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'items' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'name' => 'varchar(255) NOT NULL',
        'description' => 'text',
        'price' => 'decimal(10,2) NOT NULL',
        'unit' => 'varchar(50)',
        'category' => 'varchar(100)',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'invoices' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'customer_id' => 'varchar(255) NOT NULL',
        'invoice_number' => 'varchar(100) NOT NULL',
        'status' => 'enum("draft","sent","paid","overdue","cancelled") DEFAULT "draft"',
        'total_amount' => 'decimal(10,2) NOT NULL',
        'due_date' => 'date',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'quotations' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'customer_id' => 'varchar(255) NOT NULL',
        'quotation_number' => 'varchar(100) NOT NULL',
        'status' => 'enum("draft","sent","accepted","rejected","expired") DEFAULT "draft"',
        'total_amount' => 'decimal(10,2) NOT NULL',
        'valid_until' => 'date',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ],
    
    'contracts' => [
        'id' => 'int(11) AUTO_INCREMENT PRIMARY KEY',
        'object_id' => 'varchar(255) UNIQUE NOT NULL',
        'company_id' => 'varchar(255) NOT NULL',
        'customer_id' => 'varchar(255) NOT NULL',
        'contract_number' => 'varchar(100) NOT NULL',
        'title' => 'varchar(255) NOT NULL',
        'status' => 'enum("draft","active","completed","cancelled") DEFAULT "draft"',
        'value' => 'decimal(10,2) NOT NULL',
        'start_date' => 'date',
        'end_date' => 'date',
        'created_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ]
];

$validationResults = [];
$tablesCreated = 0;
$tablesFixed = 0;

foreach ($requiredTables as $tableName => $columns) {
    echo "Checking table: $tableName\n";
    
    // Check if table exists
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    
    if (!$result || $result->num_rows === 0) {
        // Table doesn't exist, create it
        echo "  ❌ Table missing, creating...\n";
        
        $columnDefs = [];
        foreach ($columns as $columnName => $columnDef) {
            $columnDefs[] = "`$columnName` $columnDef";
        }
        
        $sql = "CREATE TABLE `$tableName` (\n  " . implode(",\n  ", $columnDefs) . "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql)) {
            echo "  ✅ Table created successfully\n";
            $tablesCreated++;
            $validationResults[$tableName] = 'created';
        } else {
            echo "  ❌ Failed to create table: " . $conn->error . "\n";
            $validationResults[$tableName] = 'failed';
        }
    } else {
        echo "  ✅ Table exists\n";
        
        // Check if essential columns exist
        $result = $conn->query("DESCRIBE $tableName");
        $existingColumns = [];
        while ($row = $result->fetch_assoc()) {
            $existingColumns[] = $row['Field'];
        }
        
        $missingColumns = array_diff(array_keys($columns), $existingColumns);
        
        if (!empty($missingColumns)) {
            echo "  ⚠️  Missing columns: " . implode(', ', $missingColumns) . "\n";
            // Note: In production, you might want to add missing columns
            // For now, just log the issue
            $validationResults[$tableName] = 'missing_columns';
        } else {
            $validationResults[$tableName] = 'ok';
        }
        
        // Check record count
        $result = $conn->query("SELECT COUNT(*) as count FROM $tableName");
        $count = $result->fetch_assoc()['count'];
        echo "  📊 Records: $count\n";
    }
    
    echo "\n";
}

// Summary
echo "=== Validation Summary ===\n";
echo "Tables created: $tablesCreated\n";
echo "Tables with issues: " . count(array_filter($validationResults, function($status) {
    return $status !== 'ok' && $status !== 'created';
})) . "\n";

foreach ($validationResults as $table => $status) {
    $icon = $status === 'ok' || $status === 'created' ? '✅' : '⚠️';
    echo "$icon $table: $status\n";
}

echo "\n✅ Database schema validation completed!\n";

$conn->close();
?>
